// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSGameInstance.h"

#ifdef ARMORWARS_RTSGameInstance_generated_h
#error "RTSGameInstance.generated.h already included, missing '#pragma once' in RTSGameInstance.h"
#endif
#define ARMORWARS_RTSGameInstance_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class URTSEconomySubsystem;
class URTSTeamManager;

// ********** Begin Class URTSGameInstance *********************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h_20_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetEconomySubsystem); \
	DECLARE_FUNCTION(execGetTeamManager);


#define FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h_20_CALLBACK_WRAPPERS
ARMORWARS_API UClass* Z_Construct_UClass_URTSGameInstance_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h_20_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSGameInstance(); \
	friend struct Z_Construct_UClass_URTSGameInstance_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSGameInstance_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSGameInstance, UGameInstance, COMPILED_IN_FLAGS(0 | CLASS_Transient), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSGameInstance_NoRegister) \
	DECLARE_SERIALIZER(URTSGameInstance)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h_20_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSGameInstance(URTSGameInstance&&) = delete; \
	URTSGameInstance(const URTSGameInstance&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSGameInstance); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSGameInstance); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSGameInstance) \
	NO_API virtual ~URTSGameInstance();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h_17_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h_20_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h_20_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h_20_CALLBACK_WRAPPERS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h_20_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h_20_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSGameInstance;

// ********** End Class URTSGameInstance ***********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
