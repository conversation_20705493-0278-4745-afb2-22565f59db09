// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h"
#include "Subsystems/GameInstanceSubsystem.h"

#include "RTSEconomySubsystem.h"
#include "UnitDatabaseSubsystem.generated.h"

// Data table row for unit definitions
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSUnitDefinition : public FTableRowBase
{
    GENERATED_BODY()

    // Constructor with default values
    FRTSUnitDefinition()
    {
        UnitClass = nullptr;
        DisplayName = FText::FromString(TEXT("Unknown Unit"));
        Description = FText::FromString(TEXT("No description available"));
        Icon = nullptr;
        Health = 100.0f;
        AttackDamage = 10.0f;
        AttackRange = 500.0f;
        AttackRate = 1.0f;
        MoveSpeed = 300.0f;
        ProductionTime = 10.0f;
        // ResourceCost and MaintenanceCost will use their own default constructors
        // UnitTags will be empty by default
    }

    // Unit class to spawn
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unit")
    TSubclassOf<AActor> UnitClass;

    // Unit name
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unit")
    FText DisplayName;

    // Unit description
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unit")
    FText Description;

    // Unit icon
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unit")
    UTexture2D* Icon;

    // Unit stats
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float Health;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float AttackDamage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float AttackRange;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float AttackRate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float MoveSpeed;

    // Production cost (Power and Matter)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Production")
    FRTSResourceCost ResourceCost;

    // Production time in seconds
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Production")
    float ProductionTime;

    // Resource drain per second while unit is active (maintenance cost)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Production")
    FRTSResourceCost MaintenanceCost;

    // Unit type tags
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type")
    TArray<FName> UnitTags;
};

/**
 * Subsystem for managing unit data across the game
 */
UCLASS()
class ARMORWARS_API UUnitDatabaseSubsystem : public UGameInstanceSubsystem
{
    GENERATED_BODY()
    
public:
    // Initialize the subsystem
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    
    // Deinitialize the subsystem
    virtual void Deinitialize() override;
    
    // Get unit definition by ID
    UFUNCTION(BlueprintCallable, Category = "RTS|Units")
    FRTSUnitDefinition GetUnitDefinition(FName UnitID) const;
    
    // Get all unit definitions
    UFUNCTION(BlueprintCallable, Category = "RTS|Units")
    TArray<FRTSUnitDefinition> GetAllUnitDefinitions() const;
    
    // Get unit definitions by tag
    UFUNCTION(BlueprintCallable, Category = "RTS|Units")
    TArray<FRTSUnitDefinition> GetUnitDefinitionsByTag(FName Tag) const;
    
    // Check if unit template is available (Mass Entity system removed)
    bool GetUnitTemplate(FName UnitID, UWorld* World);

    // Blueprint wrapper - Prepare unit template for spawning
    UFUNCTION(BlueprintCallable, Category = "RTS|Units")
    bool PrepareUnitTemplate(FName UnitID, UWorld* World);

    // Blueprint wrapper - Check if unit template is ready
    UFUNCTION(BlueprintCallable, Category = "RTS|Units", BlueprintPure)
    bool IsUnitTemplateReady(FName UnitID) const;

    // Spawn a unit (returns spawned actor)
    UFUNCTION(BlueprintCallable, Category = "RTS|Units")
    AActor* SpawnUnit(FName UnitID, FVector Location, FRotator Rotation, UWorld* World);

    // Blueprint wrapper - Spawn unit and return success status
    UFUNCTION(BlueprintCallable, Category = "RTS|Units")
    bool SpawnUnitSimple(FName UnitID, FVector Location, FRotator Rotation, UWorld* World);

    // Economy integration functions
    UFUNCTION(BlueprintCallable, Category = "RTS|Units")
    FRTSResourceCost GetUnitResourceCost(FName UnitID) const;

    UFUNCTION(BlueprintCallable, Category = "RTS|Units")
    FRTSResourceCost GetUnitMaintenanceCost(FName UnitID) const;

    UFUNCTION(BlueprintCallable, Category = "RTS|Units")
    bool CanAffordUnit(FName UnitID, int32 PlayerID) const;

    UFUNCTION(BlueprintCallable, Category = "RTS|Units")
    AActor* SpawnUnitWithEconomy(FName UnitID, FVector Location, FRotator Rotation, UWorld* World, int32 PlayerID);

    UFUNCTION(BlueprintCallable, Category = "RTS|Units")
    bool StartUnitProduction(FName UnitID, int32 PlayerID, float& OutProductionTime);

private:
    // Data table containing unit definitions
    UPROPERTY()
    UDataTable* UnitDefinitionsTable;
    
    // Mass Entity template cache removed
    
    // Load unit definitions
    void LoadUnitDefinitions();
    

};
