// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSSelectionSystem.h"
#include "Interfaces/RTSUnitSelectionInterface.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSSelectionSystem() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSPlayerController_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSSelectionSystem();
ARMORWARS_API UClass* Z_Construct_UClass_URTSSelectionSystem_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSUnitSelectionInterface_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSSelectionFilter();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSSelectionGroup();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Delegate FOnRTSSelectionChanged ************************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnRTSSelectionChanged_Parms
	{
		TArray<ARTSBaseActor*> NewSelection;
		TArray<ARTSBaseActor*> PreviousSelection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate for selection changed events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate for selection changed events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSelection_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviousSelection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewSelection_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NewSelection;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PreviousSelection_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PreviousSelection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_NewSelection_Inner = { "NewSelection", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_NewSelection = { "NewSelection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnRTSSelectionChanged_Parms, NewSelection), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSelection_MetaData), NewProp_NewSelection_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_PreviousSelection_Inner = { "PreviousSelection", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_PreviousSelection = { "PreviousSelection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnRTSSelectionChanged_Parms, PreviousSelection), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviousSelection_MetaData), NewProp_PreviousSelection_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_NewSelection_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_NewSelection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_PreviousSelection_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_PreviousSelection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnRTSSelectionChanged__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnRTSSelectionChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnRTSSelectionChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRTSSelectionChanged_DelegateWrapper(const FMulticastScriptDelegate& OnRTSSelectionChanged, TArray<ARTSBaseActor*> const& NewSelection, TArray<ARTSBaseActor*> const& PreviousSelection)
{
	struct _Script_ArmorWars_eventOnRTSSelectionChanged_Parms
	{
		TArray<ARTSBaseActor*> NewSelection;
		TArray<ARTSBaseActor*> PreviousSelection;
	};
	_Script_ArmorWars_eventOnRTSSelectionChanged_Parms Parms;
	Parms.NewSelection=NewSelection;
	Parms.PreviousSelection=PreviousSelection;
	OnRTSSelectionChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRTSSelectionChanged **************************************************

// ********** Begin Class URTSSelectionSystem Function FindUnitsInRadius ***************************
struct Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics
{
	struct RTSSelectionSystem_eventFindUnitsInRadius_Parms
	{
		FVector CenterLocation;
		float Radius;
		ERTSSelectionFilter Filter;
		TArray<ARTSBaseActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "CPP_Default_Filter", "None" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Filter_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Filter;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_CenterLocation = { "CenterLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInRadius_Parms, CenterLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterLocation_MetaData), NewProp_CenterLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Filter_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Filter = { "Filter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInRadius_Parms, Filter), Z_Construct_UEnum_ArmorWars_ERTSSelectionFilter, METADATA_PARAMS(0, nullptr) }; // 1280946198
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_CenterLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Filter_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Filter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "FindUnitsInRadius", Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::RTSSelectionSystem_eventFindUnitsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::RTSSelectionSystem_eventFindUnitsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execFindUnitsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CenterLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_ENUM(ERTSSelectionFilter,Z_Param_Filter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->FindUnitsInRadius(Z_Param_Out_CenterLocation,Z_Param_Radius,ERTSSelectionFilter(Z_Param_Filter));
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function FindUnitsInRadius *****************************

// ********** Begin Class URTSSelectionSystem Function FindUnitsInSelectionBox *********************
struct Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics
{
	struct RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms
	{
		FVector2D StartPos;
		FVector2D EndPos;
		ERTSSelectionFilter Filter;
		TArray<ARTSBaseActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Selection box functionality\n" },
#endif
		{ "CPP_Default_Filter", "None" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selection box functionality" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Filter_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Filter;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms, StartPos), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms, EndPos), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_Filter_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_Filter = { "Filter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms, Filter), Z_Construct_UEnum_ArmorWars_ERTSSelectionFilter, METADATA_PARAMS(0, nullptr) }; // 1280946198
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_Filter_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_Filter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "FindUnitsInSelectionBox", Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execFindUnitsInSelectionBox)
{
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_EndPos);
	P_GET_ENUM(ERTSSelectionFilter,Z_Param_Filter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->FindUnitsInSelectionBox(Z_Param_Out_StartPos,Z_Param_Out_EndPos,ERTSSelectionFilter(Z_Param_Filter));
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function FindUnitsInSelectionBox ***********************

// ********** Begin Class URTSSelectionSystem Function GetPlayerController *************************
struct Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics
{
	struct RTSSelectionSystem_eventGetPlayerController_Parms
	{
		ARTSPlayerController* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventGetPlayerController_Parms, ReturnValue), Z_Construct_UClass_ARTSPlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "GetPlayerController", Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::RTSSelectionSystem_eventGetPlayerController_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::RTSSelectionSystem_eventGetPlayerController_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execGetPlayerController)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSPlayerController**)Z_Param__Result=P_THIS->GetPlayerController();
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function GetPlayerController ***************************

// ********** Begin Class URTSSelectionSystem Function SetPlayerController *************************
struct Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics
{
	struct RTSSelectionSystem_eventSetPlayerController_Parms
	{
		ARTSPlayerController* PlayerController;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Additional C++ methods for direct access\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Additional C++ methods for direct access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSetPlayerController_Parms, PlayerController), Z_Construct_UClass_ARTSPlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::NewProp_PlayerController,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "SetPlayerController", Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::RTSSelectionSystem_eventSetPlayerController_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::RTSSelectionSystem_eventSetPlayerController_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execSetPlayerController)
{
	P_GET_OBJECT(ARTSPlayerController,Z_Param_PlayerController);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPlayerController(Z_Param_PlayerController);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function SetPlayerController ***************************

// ********** Begin Class URTSSelectionSystem ******************************************************
void URTSSelectionSystem::StaticRegisterNativesURTSSelectionSystem()
{
	UClass* Class = URTSSelectionSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "FindUnitsInRadius", &URTSSelectionSystem::execFindUnitsInRadius },
		{ "FindUnitsInSelectionBox", &URTSSelectionSystem::execFindUnitsInSelectionBox },
		{ "GetPlayerController", &URTSSelectionSystem::execGetPlayerController },
		{ "SetPlayerController", &URTSSelectionSystem::execSetPlayerController },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSSelectionSystem;
UClass* URTSSelectionSystem::GetPrivateStaticClass()
{
	using TClass = URTSSelectionSystem;
	if (!Z_Registration_Info_UClass_URTSSelectionSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSSelectionSystem"),
			Z_Registration_Info_UClass_URTSSelectionSystem.InnerSingleton,
			StaticRegisterNativesURTSSelectionSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSSelectionSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSSelectionSystem_NoRegister()
{
	return URTSSelectionSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSSelectionSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * RTS Selection System - Manages unit selection for RTS gameplay\n * Implements the IRTSUnitSelectionInterface for Blueprint compatibility\n */" },
#endif
		{ "IncludePath", "RTSSelectionSystem.h" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RTS Selection System - Manages unit selection for RTS gameplay\nImplements the IRTSUnitSelectionInterface for Blueprint compatibility" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSelectionChangedEvent_MetaData[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Selection events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selection events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedUnits_MetaData[] = {
		{ "Category", "Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Currently selected units\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Currently selected units" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionGroups_MetaData[] = {
		{ "Category", "Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Selection groups (hotkeys 1-9)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selection groups (hotkeys 1-9)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwningPlayerController_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Owning player controller\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Owning player controller" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHighlightSelectedUnits_MetaData[] = {
		{ "Category", "Selection Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Selection settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selection settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Selection Settings" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSelectionChangedEvent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SelectedUnits_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SelectedUnits;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SelectionGroups_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SelectionGroups_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SelectionGroups;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwningPlayerController;
	static void NewProp_bHighlightSelectedUnits_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHighlightSelectedUnits;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius, "FindUnitsInRadius" }, // 1074043033
		{ &Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox, "FindUnitsInSelectionBox" }, // 3199806915
		{ &Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController, "GetPlayerController" }, // 2753863605
		{ &Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController, "SetPlayerController" }, // 1764212049
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static const UECodeGen_Private::FImplementedInterfaceParams InterfaceParams[];
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSSelectionSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_OnSelectionChangedEvent = { "OnSelectionChangedEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSSelectionSystem, OnSelectionChangedEvent), Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSelectionChangedEvent_MetaData), NewProp_OnSelectionChangedEvent_MetaData) }; // 1719768022
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectedUnits_Inner = { "SelectedUnits", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectedUnits = { "SelectedUnits", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSSelectionSystem, SelectedUnits), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedUnits_MetaData), NewProp_SelectedUnits_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups_ValueProp = { "SelectionGroups", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FRTSSelectionGroup, METADATA_PARAMS(0, nullptr) }; // 974660342
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups_Key_KeyProp = { "SelectionGroups_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups = { "SelectionGroups", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSSelectionSystem, SelectionGroups), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionGroups_MetaData), NewProp_SelectionGroups_MetaData) }; // 974660342
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_OwningPlayerController = { "OwningPlayerController", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSSelectionSystem, OwningPlayerController), Z_Construct_UClass_ARTSPlayerController_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwningPlayerController_MetaData), NewProp_OwningPlayerController_MetaData) };
void Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bHighlightSelectedUnits_SetBit(void* Obj)
{
	((URTSSelectionSystem*)Obj)->bHighlightSelectedUnits = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bHighlightSelectedUnits = { "bHighlightSelectedUnits", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSSelectionSystem), &Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bHighlightSelectedUnits_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHighlightSelectedUnits_MetaData), NewProp_bHighlightSelectedUnits_MetaData) };
void Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((URTSSelectionSystem*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSSelectionSystem), &Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSSelectionSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_OnSelectionChangedEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectedUnits_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectedUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_OwningPlayerController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bHighlightSelectedUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bEnableDebugLogging,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSSelectionSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSSelectionSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSSelectionSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FImplementedInterfaceParams Z_Construct_UClass_URTSSelectionSystem_Statics::InterfaceParams[] = {
	{ Z_Construct_UClass_URTSUnitSelectionInterface_NoRegister, (int32)VTABLE_OFFSET(URTSSelectionSystem, IRTSUnitSelectionInterface), false },  // 1723694110
};
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSSelectionSystem_Statics::ClassParams = {
	&URTSSelectionSystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSSelectionSystem_Statics::PropPointers,
	InterfaceParams,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSSelectionSystem_Statics::PropPointers),
	UE_ARRAY_COUNT(InterfaceParams),
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSSelectionSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSSelectionSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSSelectionSystem()
{
	if (!Z_Registration_Info_UClass_URTSSelectionSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSSelectionSystem.OuterSingleton, Z_Construct_UClass_URTSSelectionSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSSelectionSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSSelectionSystem);
URTSSelectionSystem::~URTSSelectionSystem() {}
// ********** End Class URTSSelectionSystem ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h__Script_ArmorWars_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSSelectionSystem, URTSSelectionSystem::StaticClass, TEXT("URTSSelectionSystem"), &Z_Registration_Info_UClass_URTSSelectionSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSSelectionSystem), 1447333425U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h__Script_ArmorWars_873601268(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
