// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/GameInstance.h"
#include "RTSGameInstance.generated.h"

class URTSTeamManager;
class URTSEconomySubsystem;


/**
 * Game Instance for ArmorWars RTS game
 * Manages game-wide systems and persistent data across levels
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSGameInstance : public UGameInstance
{
	GENERATED_BODY()

public:
	URTSGameInstance();

	// UGameInstance interface
	virtual void Init() override;
	virtual void Shutdown() override;

protected:
	/** Called when the game instance is initialized */
	UFUNCTION(BlueprintImplementableEvent, Category = "RTS Game Instance")
	void OnGameInstanceInitialized();

	/** Called when the game instance is shutting down */
	UFUNCTION(BlueprintImplementableEvent, Category = "RTS Game Instance")
	void OnGameInstanceShutdown();

public:
	/** Get the team manager subsystem */
	UFUNCTION(BlueprintCallable, Category = "RTS Game Instance")
	URTSTeamManager* GetTeamManager() const;

	/** Get the economy subsystem */
	UFUNCTION(BlueprintCallable, Category = "RTS Game Instance")
	URTSEconomySubsystem* GetEconomySubsystem() const;



protected:
	/** Whether debug logging is enabled for the game instance */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
	bool bEnableDebugLogging = false;
};
