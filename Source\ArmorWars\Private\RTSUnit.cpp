#include "RTSUnit.h"
#include "RTSArmorComponent.h"
#include "RTSUnitAIComponent.h"
#include "RTSWeaponController.h"
#include "RTSTacticalAIComponent.h"
#include "RTSAIController.h"
#include "RTSLandMovementComponent.h"
#include "RTSAirMovementComponent.h"
#include "RTSSeaMovementComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/PawnMovementComponent.h"
#include "Engine/World.h"
#include "Engine/DamageEvents.h"
#include "Kismet/GameplayStatics.h"

ARTSUnit::ARTSUnit()
{
    PrimaryActorTick.bCanEverTick = true;

    // Set actor type to Unit
    ActorType = ERTSActorType::Unit;

    // Initialize health
    MaxHealth = 100.0f;
    CurrentHealth = MaxHealth;

    // Create components
    // Create collision component first (as root)
    CollisionComponent = CreateDefaultSubobject<UCapsuleComponent>(TEXT("CollisionComponent"));
    CollisionComponent->SetCapsuleSize(40.0f, 60.0f); // Smaller radius and height
    CollisionComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    CollisionComponent->SetCollisionResponseToAllChannels(ECR_Ignore);
    CollisionComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block); // Only block other pawns
    CollisionComponent->SetCollisionResponseToChannel(ECC_WorldStatic, ECR_Block); // Block world geometry
    CollisionComponent->SetCollisionResponseToChannel(ECC_WorldDynamic, ECR_Block); // Block dynamic objects
    RootComponent = CollisionComponent;

    // Create mesh component and attach to collision
    MeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MeshComponent"));
    MeshComponent->SetupAttachment(CollisionComponent);
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision); // Mesh doesn't need collision

    // Create default land movement component (subclasses will override)
    // Note: This will be replaced in subclasses with appropriate movement components

    ArmorComponent = CreateDefaultSubobject<URTSArmorComponent>(TEXT("ArmorComponent"));
    AIComponent = CreateDefaultSubobject<URTSUnitAIComponent>(TEXT("AIComponent"));
    TacticalAIComponent = CreateDefaultSubobject<URTSTacticalAIComponent>(TEXT("TacticalAIComponent"));
    WeaponController = CreateDefaultSubobject<URTSWeaponController>(TEXT("WeaponController"));

    // Set default AI controller class
    AIControllerClass = ARTSAIController::StaticClass();
}

void ARTSUnit::BeginPlay()
{
    Super::BeginPlay();

    // Basic component configuration - domain-specific config is in subclasses
    ConfigureComponentsForUnitType();

    // Movement component removed - using direct actor movement
    // Movement settings are now handled directly in UpdateMovement()
}

// Health Functions (from RTSBaseActor)
float ARTSUnit::TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser)
{
    if (DamageAmount <= 0.0f || IsDead())
    {
        return 0.0f;
    }

    float OldHealth = CurrentHealth;
    CurrentHealth = FMath::Clamp(CurrentHealth - DamageAmount, 0.0f, MaxHealth);

    // Broadcast events
    OnHealthChanged.Broadcast(CurrentHealth, MaxHealth);
    OnDamageReceived.Broadcast(DamageAmount, DamageCauser);
    OnTakeDamage(DamageAmount, DamageCauser);

    // Check for death
    if (CurrentHealth <= 0.0f && OldHealth > 0.0f)
    {
        HandleDeath();
    }

    return DamageAmount;
}

void ARTSUnit::TakeDamageSimple(float DamageAmount, AActor* DamageSource)
{
    FDamageEvent DamageEvent;
    TakeDamage(DamageAmount, DamageEvent, nullptr, DamageSource);
}

void ARTSUnit::Heal(float HealAmount)
{
    if (HealAmount <= 0.0f || IsDead())
    {
        return;
    }

    CurrentHealth = FMath::Clamp(CurrentHealth + HealAmount, 0.0f, MaxHealth);

    // Broadcast events
    OnHealthChanged.Broadcast(CurrentHealth, MaxHealth);
    OnHealed(HealAmount);
}

void ARTSUnit::SetHealth(float NewHealth)
{
    float OldHealth = CurrentHealth;
    CurrentHealth = FMath::Clamp(NewHealth, 0.0f, MaxHealth);

    // Broadcast health changed event
    OnHealthChanged.Broadcast(CurrentHealth, MaxHealth);

    // Check for death
    if (CurrentHealth <= 0.0f && OldHealth > 0.0f)
    {
        HandleDeath();
    }
}

// Team Functions (from RTSBaseActor)
bool ARTSUnit::IsOnSameTeam(const AActor* OtherActor) const
{
    if (!OtherActor)
    {
        return false;
    }

    // Check if the other actor is an RTSUnit
    if (const ARTSUnit* OtherUnit = Cast<ARTSUnit>(OtherActor))
    {
        return TeamID == OtherUnit->GetTeamID();
    }

    // Check if the other actor is an RTSBaseActor
    if (const ARTSBaseActor* OtherBaseActor = Cast<ARTSBaseActor>(OtherActor))
    {
        // TODO: Fix type compatibility between RTSUnit and RTSBaseActor
        return TeamID == OtherBaseActor->TeamID;
    }

    return false;
}

bool ARTSUnit::IsEnemy(const AActor* OtherActor) const
{
    return !IsOnSameTeam(OtherActor);
}

// Selection Functions (from RTSBaseActor)
void ARTSUnit::SetSelected(bool bSelected)
{
    if (bIsSelected != bSelected)
    {
        bIsSelected = bSelected;
        OnSelectionChanged.Broadcast(this, bSelected);
    }
}

// Protected Functions
void ARTSUnit::HandleDeath()
{
    // Deselect if selected
    if (bIsSelected)
    {
        SetSelected(false);
    }

    // Broadcast death event
    OnActorDeath.Broadcast(this);
    OnDeath();

    // Log death for debugging
    UE_LOG(LogTemp, Log, TEXT("%s has died"), *GetName());
}

void ARTSUnit::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    if (IsAlive())
    {
        // Debug: Check if Tick is being called for moving units
        static int32 TickCounter = 0;
        TickCounter++;
        if (bIsMoving && TickCounter % 60 == 0) // Log every 60 frames for moving units
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s Tick called - DeltaTime: %.3f, bIsMoving: %s"),
                *GetName(), DeltaTime, bIsMoving ? TEXT("true") : TEXT("false"));
        }

        UpdateMovement(DeltaTime);
        UpdateCombat(DeltaTime);
    }
}

// Movement Functions
void ARTSUnit::MoveToLocation(const FVector& Location)
{
    if (!IsAlive())
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s cannot move - not alive"), *GetName());
        return;
    }

    // Determine final target location (formation-aware)
    FVector FinalTargetLocation = Location;
    if (bIsInFormation)
    {
        FinalTargetLocation = Location + FormationOffset;
        UE_LOG(LogTemp, Log, TEXT("RTSUnit: %s moving to formation position %s (base: %s, offset: %s)"),
            *GetName(), *FinalTargetLocation.ToString(), *Location.ToString(), *FormationOffset.ToString());
    }

    // Set RTSUnit movement state regardless of movement component
    TargetLocation = FinalTargetLocation;
    bIsMoving = true;
    SetMovementState(ERTSMovementState::TurningInPlace); // Start by turning to face target

    // Use movement component if available
    if (UPawnMovementComponent* MovementComp = GetMovementComponent())
    {
        // Try to cast to our custom movement components
        if (URTSLandMovementComponent* LandMovement = Cast<URTSLandMovementComponent>(MovementComp))
        {
            LandMovement->SetAITargetLocation(FinalTargetLocation);
        }
        else if (URTSAirMovementComponent* AirMovement = Cast<URTSAirMovementComponent>(MovementComp))
        {
            AirMovement->SetAITargetLocation(FinalTargetLocation);
        }
        else if (URTSSeaMovementComponent* SeaMovement = Cast<URTSSeaMovementComponent>(MovementComp))
        {
            SeaMovement->SetAITargetLocation(FinalTargetLocation);
        }
        else
        {
            // Fallback to direct movement for other movement components
            FallbackMoveToLocation(FinalTargetLocation);
        }
    }
    else
    {
        // Fallback to direct movement if no movement component
        FallbackMoveToLocation(FinalTargetLocation);
    }

    // Broadcast events
    OnMovementChanged.Broadcast(this, FinalTargetLocation);
    OnMovementStarted(FinalTargetLocation);
}

void ARTSUnit::FallbackMoveToLocation(const FVector& Location)
{
    // Stop any current movement first
    if (bIsMoving)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s stopping current movement to start new movement"), *GetName());
        StopMovement();
    }

    // Set target location and start moving
    TargetLocation = Location;
    bIsMoving = true;
    SetMovementState(ERTSMovementState::TurningInPlace); // Start by turning to face target

    float Distance = FVector::Dist(GetActorLocation(), Location);
    UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s starting fallback movement to %s (Distance: %.2f, Current: %s)"),
        *GetName(), *Location.ToString(), Distance, *GetActorLocation().ToString());

    // Validate the target location
    if (Distance < ArrivalDistance)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s target too close (%.2f < %.2f), not moving"),
            *GetName(), Distance, ArrivalDistance);
        bIsMoving = false;
        return;
    }
}

void ARTSUnit::MoveToLocationSynchronized(const FVector& Location, float SynchronizedSpeed)
{
    if (!IsAlive())
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s cannot move - not alive"), *GetName());
        return;
    }

    // Stop any current movement first
    if (bIsMoving)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s stopping current movement to start synchronized movement"), *GetName());
        StopMovement();
    }

    // Determine final target location (formation-aware)
    FVector FinalTargetLocation = Location;
    if (bIsInFormation)
    {
        FinalTargetLocation = Location + FormationOffset;
        UE_LOG(LogTemp, Log, TEXT("RTSUnit: %s synchronized movement to formation position %s (base: %s, offset: %s)"),
            *GetName(), *FinalTargetLocation.ToString(), *Location.ToString(), *FormationOffset.ToString());
    }

    // Set target location and synchronized movement
    TargetLocation = FinalTargetLocation;
    bIsMoving = true;
    bUseSynchronizedMovement = true;
    SynchronizedMovementSpeed = SynchronizedSpeed;
    SetMovementState(ERTSMovementState::TurningInPlace); // Start by turning to face target

    float Distance = FVector::Dist(GetActorLocation(), FinalTargetLocation);
    UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s starting synchronized movement to %s (Distance: %.2f, Speed: %.2f)"),
        *GetName(), *FinalTargetLocation.ToString(), Distance, SynchronizedSpeed);

    // Validate the target location
    if (Distance < ArrivalDistance)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s target too close (%.2f < %.2f), not moving"),
            *GetName(), Distance, ArrivalDistance);
        bIsMoving = false;
        bUseSynchronizedMovement = false;
        return;
    }

    // Also set the movement component target
    if (UPawnMovementComponent* MovementComp = GetMovementComponent())
    {
        if (URTSLandMovementComponent* LandMovement = Cast<URTSLandMovementComponent>(MovementComp))
        {
            LandMovement->SetAITargetLocation(FinalTargetLocation);
        }
        else if (URTSAirMovementComponent* AirMovement = Cast<URTSAirMovementComponent>(MovementComp))
        {
            AirMovement->SetAITargetLocation(FinalTargetLocation);
        }
        else if (URTSSeaMovementComponent* SeaMovement = Cast<URTSSeaMovementComponent>(MovementComp))
        {
            SeaMovement->SetAITargetLocation(FinalTargetLocation);
        }
    }

    // Broadcast events
    OnMovementChanged.Broadcast(this, FinalTargetLocation);
    OnMovementStarted(FinalTargetLocation);
}

void ARTSUnit::StopMovement()
{
    // Use movement component if available
    if (UPawnMovementComponent* MovementComp = GetMovementComponent())
    {
        // Try to cast to our custom movement components
        if (URTSLandMovementComponent* LandMovement = Cast<URTSLandMovementComponent>(MovementComp))
        {
            LandMovement->StopMovement();
        }
        else if (URTSAirMovementComponent* AirMovement = Cast<URTSAirMovementComponent>(MovementComp))
        {
            AirMovement->StopMovement();
        }
        else if (URTSSeaMovementComponent* SeaMovement = Cast<URTSSeaMovementComponent>(MovementComp))
        {
            SeaMovement->StopMovement();
        }
        else
        {
            // Fallback for other movement components
            MovementComp->StopMovementImmediately();
        }
    }

    // Also handle fallback movement state
    if (bIsMoving)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s stopping movement"), *GetName());
    }

    bIsMoving = false;
    bUseSynchronizedMovement = false;
    SynchronizedMovementSpeed = 0.0f;
    TargetLocation = GetActorLocation();
    SetMovementState(ERTSMovementState::Stationary);

    OnMovementStopped();
}

void ARTSUnit::AttackMoveToLocation(const FVector& Location)
{
    // Set movement target
    MoveToLocation(Location);

    // Set AI to attack-move mode
    if (AIComponent)
    {
        AIComponent->SetAttackMoveMode(true);
    }

    UE_LOG(LogTemp, Log, TEXT("RTSUnit: %s attack-moving to %s"), *GetName(), *Location.ToString());
}

bool ARTSUnit::IsMoving() const
{
    // Check movement component first
    if (UPawnMovementComponent* MovementComp = GetMovementComponent())
    {
        // Try to cast to our custom movement components
        if (const URTSLandMovementComponent* LandMovement = Cast<URTSLandMovementComponent>(MovementComp))
        {
            return LandMovement->IsMoving();
        }
        else if (const URTSAirMovementComponent* AirMovement = Cast<URTSAirMovementComponent>(MovementComp))
        {
            return AirMovement->IsMoving();
        }
        else if (const URTSSeaMovementComponent* SeaMovement = Cast<URTSSeaMovementComponent>(MovementComp))
        {
            return SeaMovement->IsMoving();
        }
        else
        {
            // Fallback for other movement components
            return !MovementComp->Velocity.IsNearlyZero();
        }
    }

    // Fallback to internal state
    return bIsMoving;
}

float ARTSUnit::GetDistanceToTarget() const
{
    return FVector::Dist(GetActorLocation(), TargetLocation);
}

// Movement state functions
void ARTSUnit::SetMovementState(ERTSMovementState NewState)
{
    if (MovementState != NewState)
    {
        ERTSMovementState OldState = MovementState;
        MovementState = NewState;

        // Only log state changes occasionally to reduce spam
        static int32 StateChangeCounter = 0;
        StateChangeCounter++;
        if (StateChangeCounter % 10 == 0) // Log every 10th state change
        {
            UE_LOG(LogTemp, Log, TEXT("RTSUnit: %s movement state changed from %d to %d"),
                *GetName(), static_cast<int32>(OldState), static_cast<int32>(NewState));
        }
    }
}

// Formation movement functions
void ARTSUnit::MoveToLocationWithFormation(const FVector& Location, const FVector& FormationOffsetParam)
{
    SetFormationData(true, FormationOffsetParam);
    MoveToLocation(Location);
}

void ARTSUnit::SetFormationData(bool bInFormation, const FVector& Offset)
{
    bIsInFormation = bInFormation;
    FormationOffset = Offset;

    UE_LOG(LogTemp, Log, TEXT("RTSUnit: %s formation data set - InFormation: %s, Offset: %s"),
        *GetName(), bInFormation ? TEXT("true") : TEXT("false"), *Offset.ToString());
}

// Collision avoidance functions
// NOTE: This collision avoidance system is WORKING and ACTIVELY USED - DO NOT REMOVE
// Prevents units from moving through each other and provides smooth unit separation
// Used by both movement components and fallback movement system
FVector ARTSUnit::CalculateCollisionAvoidance(const FVector& DesiredDirection) const
{
    // If no desired direction, return as-is
    if (DesiredDirection.IsNearlyZero())
    {
        return DesiredDirection;
    }

    FVector AvoidanceVector = FVector::ZeroVector;
    TArray<ARTSUnit*> NearbyUnits = GetNearbyUnits(CollisionAvoidanceRadius);

    FVector MyLocation = GetActorLocation();
    int32 AvoidanceCount = 0;

    for (const ARTSUnit* OtherUnit : NearbyUnits)
    {
        if (!OtherUnit || OtherUnit == this)
        {
            continue;
        }

        FVector ToOther = OtherUnit->GetActorLocation() - MyLocation;
        float Distance = ToOther.Size();

        if (Distance > 0.0f && Distance < CollisionAvoidanceRadius)
        {
            // Calculate avoidance force (stronger when closer)
            FVector AvoidanceDirection = -ToOther.GetSafeNormal();
            float AvoidanceStrength = (CollisionAvoidanceRadius - Distance) / CollisionAvoidanceRadius;
            AvoidanceVector += AvoidanceDirection * AvoidanceStrength * CollisionAvoidanceStrength;
            AvoidanceCount++;
        }
    }

    // If no avoidance needed, return original direction
    if (AvoidanceCount == 0)
    {
        return DesiredDirection;
    }

    // Normalize avoidance vector by count to prevent excessive forces
    if (AvoidanceCount > 0)
    {
        AvoidanceVector /= AvoidanceCount;
    }

    // Blend desired direction with avoidance (favor desired direction more)
    FVector FinalDirection = DesiredDirection * 0.7f + AvoidanceVector * 0.3f;
    return FinalDirection.GetSafeNormal();
}

TArray<ARTSUnit*> ARTSUnit::GetNearbyUnits(float Radius) const
{
    TArray<ARTSUnit*> NearbyUnits;

    if (!GetWorld())
    {
        return NearbyUnits;
    }

    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ARTSUnit::StaticClass(), FoundActors);

    FVector MyLocation = GetActorLocation();

    for (AActor* Actor : FoundActors)
    {
        if (ARTSUnit* OtherUnit = Cast<ARTSUnit>(Actor))
        {
            if (OtherUnit != this)
            {
                float Distance = FVector::Dist(MyLocation, OtherUnit->GetActorLocation());
                if (Distance <= Radius)
                {
                    NearbyUnits.Add(OtherUnit);
                }
            }
        }
    }

    return NearbyUnits;
}





// Domain Functions
bool ARTSUnit::IsLandUnit() const
{
    return UnitDomain == ERTSUnitDomain::Land;
}

bool ARTSUnit::IsAirUnit() const
{
    return UnitDomain == ERTSUnitDomain::Air;
}

bool ARTSUnit::IsSeaUnit() const
{
    return UnitDomain == ERTSUnitDomain::Sea;
}

bool ARTSUnit::IsSubnauticalUnit() const
{
    return UnitDomain == ERTSUnitDomain::Subnautical;
}

// DEPRECATED: Aircraft-specific functions moved to ARTSAirUnit subclass
bool ARTSUnit::IsAircraft() const
{
    return UnitDomain == ERTSUnitDomain::Air;
}

// Combat Functions using Weapon Controller
void ARTSUnit::AttackTarget(AActor* Target)
{
    if (!Target || !WeaponController)
    {
        return;
    }

    // Convert AActor* to ARTSBaseActor* for weapon controller
    if (ARTSBaseActor* BaseTarget = Cast<ARTSBaseActor>(Target))
    {
        if (WeaponController->FireAtTarget(BaseTarget))
        {
            // TODO: Add OnCombatChanged delegate
            // OnCombatChanged.Broadcast(this, Target);
            OnAttackStarted(BaseTarget);
        }
    }
}

void ARTSUnit::StopAttacking()
{
    if (WeaponController)
    {
        WeaponController->StopFiring();
        WeaponController->ClearTarget();
        OnAttackStopped();
    }
}

bool ARTSUnit::CanAttackTarget(const AActor* Target) const
{
    if (!Target || IsOnSameTeam(Target) || !WeaponController)
    {
        return false;
    }

    // Check if target is alive
    bool bTargetAlive = false;
    if (const ARTSUnit* TargetUnit = Cast<ARTSUnit>(Target))
    {
        bTargetAlive = TargetUnit->IsAlive();
    }
    else if (const ARTSBaseActor* TargetActor = Cast<ARTSBaseActor>(Target))
    {
        bTargetAlive = TargetActor->IsAlive();
    }

    if (!bTargetAlive)
    {
        return false;
    }

    // Check if we can target this type of unit
    // Check if target is another unit (ARTSUnit) or building (ARTSBaseActor)
    if (const ARTSUnit* TargetUnit = Cast<ARTSUnit>(Target))
    {
        return WeaponController->CanTargetDomain(TargetUnit->UnitDomain);
    }

    // Buildings can always be targeted if we have land targeting capability
    if (const ARTSBaseActor* TargetActor = Cast<ARTSBaseActor>(Target))
    {
        if (TargetActor->IsBuilding())
        {
            return WeaponController->CanTargetDomain(ERTSUnitDomain::Land);
        }
    }

    return true;
}

bool ARTSUnit::IsInAttackRange(const AActor* Target) const
{
    if (!Target || !WeaponController)
    {
        return false;
    }

    float Distance = FVector::Dist(GetActorLocation(), Target->GetActorLocation());
    return Distance <= WeaponController->GetMaxRange();
}

bool ARTSUnit::IsAttacking() const
{
    return WeaponController && WeaponController->IsFiring();
}

bool ARTSUnit::HasWeapons() const
{
    return WeaponController && WeaponController->GetWeaponCount() > 0;
}

AActor* ARTSUnit::GetCurrentTarget() const
{
    return WeaponController ? Cast<AActor>(WeaponController->GetCurrentTarget()) : nullptr;
}

float ARTSUnit::GetMaxAttackRange() const
{
    return WeaponController ? WeaponController->GetMaxRange() : 0.0f;
}

float ARTSUnit::GetTotalDamagePerSecond() const
{
    return WeaponController ? WeaponController->GetTotalDamagePerSecond() : 0.0f;
}

bool ARTSUnit::CanTargetDomain(ERTSUnitDomain Domain) const
{
    return WeaponController && WeaponController->CanTargetDomain(Domain);
}

float ARTSUnit::GetAttackRange() const
{
    return GetMaxAttackRange();
}

float ARTSUnit::GetAttackDamage() const
{
    return GetTotalDamagePerSecond();
}

TArray<ARTSBaseActor*> ARTSUnit::FindEnemiesInRange(float Range) const
{
    TArray<ARTSBaseActor*> EnemiesInRange;

    if (!GetWorld())
    {
        return EnemiesInRange;
    }

    // Find all RTS actors in range
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ARTSBaseActor::StaticClass(), FoundActors);

    FVector MyLocation = GetActorLocation();

    for (AActor* Actor : FoundActors)
    {
        if (ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor))
        {
            // Skip self and dead actors
            if (RTSActor == Cast<ARTSBaseActor>(this) || !RTSActor->IsAlive())
            {
                continue;
            }

            // Skip allies
            if (IsOnSameTeam(RTSActor))
            {
                continue;
            }

            // Check if in range
            float Distance = FVector::Dist(MyLocation, RTSActor->GetActorLocation());
            if (Distance <= Range)
            {
                EnemiesInRange.Add(RTSActor);
            }
        }
    }

    return EnemiesInRange;
}



// Protected Functions
void ARTSUnit::ConfigureComponentsForUnitType()
{
    // Basic component configuration - subclasses handle domain-specific setup

    // Configure armor component with basic defaults based on domain
    if (ArmorComponent)
    {
        switch (UnitDomain)
        {
            case ERTSUnitDomain::Land:
                ArmorComponent->SetUniformArmor(80.0f, ERTSArmorType::Heavy);
                break;
            case ERTSUnitDomain::Air:
                ArmorComponent->SetUniformArmor(20.0f, ERTSArmorType::Light);
                break;
            case ERTSUnitDomain::Sea:
                ArmorComponent->SetUniformArmor(60.0f, ERTSArmorType::Medium);
                break;
            case ERTSUnitDomain::Subnautical:
                ArmorComponent->SetUniformArmor(50.0f, ERTSArmorType::Composite);
                break;
        }
    }


}

// Protected Functions
void ARTSUnit::UpdateMovement(float DeltaTime)
{
    // NOTE: This movement system is WORKING and ACTIVELY USED - DO NOT REMOVE
    // Coordinates between movement components and fallback movement system
    // Provides proper RTS movement behavior with formation support and collision avoidance
    if (!bIsMoving)
    {
        // Set movement state to stationary when not moving
        if (MovementState != ERTSMovementState::Stationary)
        {
            SetMovementState(ERTSMovementState::Stationary);
        }
        return;
    }

    // Check if we have a movement component - if so, let it handle the movement
    if (UPawnMovementComponent* MovementComp = GetMovementComponent())
    {
        if (URTSLandMovementComponent* LandMovement = Cast<URTSLandMovementComponent>(MovementComp))
        {
            // Movement component handles the actual movement, we just track state
            UpdateMovementStateForComponent(DeltaTime);
            return;
        }
        else if (URTSAirMovementComponent* AirMovement = Cast<URTSAirMovementComponent>(MovementComp))
        {
            // Movement component handles the actual movement, we just track state
            UpdateMovementStateForComponent(DeltaTime);
            return;
        }
        else if (URTSSeaMovementComponent* SeaMovement = Cast<URTSSeaMovementComponent>(MovementComp))
        {
            // Movement component handles the actual movement, we just track state
            UpdateMovementStateForComponent(DeltaTime);
            return;
        }
    }

    // Fallback movement system (when no movement component is available)
    UpdateFallbackMovement(DeltaTime);
}

void ARTSUnit::UpdateMovementStateForComponent(float DeltaTime)
{
    // NOTE: This movement state tracking is WORKING and ACTIVELY USED - DO NOT REMOVE
    // Tracks movement state and handles arrival detection when using movement components
    // Coordinates with RTSLandMovementComponent for proper RTS movement behavior
    // When using movement components, we just track state and check for arrival
    FVector CurrentLocation = GetActorLocation();
    float DistanceToTarget = FVector::Dist(CurrentLocation, TargetLocation);

    // Debug: Log movement state every few frames
    static int32 MovementCounter = 0;
    MovementCounter++;
    if (MovementCounter % 60 == 0) // Log every 60 frames (about once per second)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s UpdateMovement called - bIsMoving: %s, State: %d, Distance: %.2f"),
            *GetName(), bIsMoving ? TEXT("true") : TEXT("false"), static_cast<int32>(MovementState), DistanceToTarget);
    }

    // Check if we've reached the destination
    if (DistanceToTarget < ArrivalDistance)
    {
        bIsMoving = false;
        bUseSynchronizedMovement = false;
        SetMovementState(ERTSMovementState::Stationary);
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s reached destination at %s"), *GetName(), *TargetLocation.ToString());

        // Stop the movement component
        if (UPawnMovementComponent* MovementComp = GetMovementComponent())
        {
            if (URTSLandMovementComponent* LandMovement = Cast<URTSLandMovementComponent>(MovementComp))
            {
                LandMovement->StopMovement();
            }
            else if (URTSAirMovementComponent* AirMovement = Cast<URTSAirMovementComponent>(MovementComp))
            {
                AirMovement->StopMovement();
            }
            else if (URTSSeaMovementComponent* SeaMovement = Cast<URTSSeaMovementComponent>(MovementComp))
            {
                SeaMovement->StopMovement();
            }
        }

        OnReachedDestination();
        return;
    }

    // Update movement state based on current behavior
    FVector CurrentRotation = GetActorRotation().Vector();
    FVector DirectionToTarget = (TargetLocation - CurrentLocation).GetSafeNormal();
    float DotProduct = FVector::DotProduct(CurrentRotation, DirectionToTarget);
    float AngleDifference = FMath::RadiansToDegrees(FMath::Acos(FMath::Clamp(DotProduct, -1.0f, 1.0f)));

    // Determine movement state
    if (AngleDifference > TurningAngleTolerance)
    {
        if (MovementState == ERTSMovementState::Moving)
        {
            SetMovementState(ERTSMovementState::TurningWhileMoving);
        }
        else
        {
            SetMovementState(ERTSMovementState::TurningInPlace);
        }
    }
    else
    {
        SetMovementState(ERTSMovementState::Moving);
    }
}

void ARTSUnit::UpdateFallbackMovement(float DeltaTime)
{
    // Debug: Confirm UpdateMovement is being called
    static int32 DebugCounter = 0;
    DebugCounter++;
    if (DebugCounter % 60 == 0) // Log every 60 frames (about once per second at 60fps)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s UpdateFallbackMovement called - bIsMoving: %s, State: %d"),
            *GetName(), bIsMoving ? TEXT("true") : TEXT("false"), static_cast<int32>(MovementState));
    }

    FVector CurrentLocation = GetActorLocation();
    FRotator CurrentRotation = GetActorRotation();
    FVector DirectionToTarget = (TargetLocation - CurrentLocation).GetSafeNormal();
    float DistanceToTarget = FVector::Dist(CurrentLocation, TargetLocation);

    UE_LOG(LogTemp, VeryVerbose, TEXT("RTSUnit: %s moving - Current: %s, Target: %s, Distance: %.2f"),
        *GetName(), *CurrentLocation.ToString(), *TargetLocation.ToString(), DistanceToTarget);

    // Check if we've reached the destination
    if (DistanceToTarget < ArrivalDistance)
    {
        bIsMoving = false;
        SetMovementState(ERTSMovementState::Stationary);
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s reached destination at %s"), *GetName(), *TargetLocation.ToString());
        OnReachedDestination();
        return;
    }

    // Calculate desired rotation (facing the target) - X axis as forward
    FRotator DesiredRotation = DirectionToTarget.Rotation();
    // No rotation offset needed - X axis is naturally forward
    DesiredRotation.Pitch = 0.0f; // Keep level for ground units
    DesiredRotation.Roll = 0.0f;

    // Calculate angle difference between current facing and desired facing
    float AngleDifference = FMath::Abs(FMath::FindDeltaAngleDegrees(CurrentRotation.Yaw, DesiredRotation.Yaw));

    // Debug: Log movement state every few frames
    static int32 MovementCounter = 0;
    MovementCounter++;
    if (MovementCounter % 30 == 0) // Log every 30 frames (about twice per second)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s movement state - Current: %.1f°, Target: %.1f°, Diff: %.1f°, Tolerance: %.1f°, State: %d"),
            *GetName(), CurrentRotation.Yaw, DesiredRotation.Yaw, AngleDifference, TurningAngleTolerance, static_cast<int32>(MovementState));
    }

    // Improved movement state machine
    bool bWasMovingForward = (MovementState == ERTSMovementState::Moving);

    // Determine if we need to turn
    bool bNeedToTurn = AngleDifference > TurningAngleTolerance;

    if (bNeedToTurn)
    {
        // Determine movement state based on current state
        if (bWasMovingForward)
        {
            // Was moving forward, now need to turn while moving
            SetMovementState(ERTSMovementState::TurningWhileMoving);
        }
        else
        {
            // Was stationary or already turning, turn in place
            SetMovementState(ERTSMovementState::TurningInPlace);
        }

        // Turn toward target
        FRotator NewRotation = FMath::RInterpTo(CurrentRotation, DesiredRotation, DeltaTime, TurnRate);
        SetActorRotation(NewRotation);

        if (MovementCounter % 30 == 0)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s TURNING - Current: %.1f°, Target: %.1f°, New: %.1f°, Diff: %.1f°, DeltaTime: %.3f, TurnRate: %.1f, State: %s"),
                *GetName(), CurrentRotation.Yaw, DesiredRotation.Yaw, NewRotation.Yaw, AngleDifference, DeltaTime, TurnRate,
                MovementState == ERTSMovementState::TurningInPlace ? TEXT("TurningInPlace") : TEXT("TurningWhileMoving"));
        }

        // If turning while moving, continue moving forward but slower
        if (MovementState == ERTSMovementState::TurningWhileMoving)
        {
            FVector ForwardVector = GetActorForwardVector();

            // Apply collision avoidance
            FVector AvoidanceDirection = CalculateCollisionAvoidance(ForwardVector);

            // Use synchronized speed if in formation, otherwise use normal speed
            float EffectiveSpeed = bUseSynchronizedMovement ? SynchronizedMovementSpeed : MovementSpeed;
            // Reduce speed while turning
            EffectiveSpeed *= 0.6f;

            float MovementDistance = EffectiveSpeed * DeltaTime;
            FVector MovementVector = AvoidanceDirection * MovementDistance;
            FVector NewLocation = CurrentLocation + MovementVector;

            // Move with minimal collision detection (only sweep for world geometry)
            bool bMoved = SetActorLocation(NewLocation, false); // false = no collision sweep for now

            if (!bMoved && MovementCounter % 60 == 0) // Reduce log spam
            {
                UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s movement blocked while turning"), *GetName());
            }
        }
    }
    else
    {
        // Facing the right direction, move forward
        SetMovementState(ERTSMovementState::Moving);

        FVector ForwardVector = GetActorForwardVector(); // Points in X direction (UE default)

        // Apply collision avoidance
        FVector AvoidanceDirection = CalculateCollisionAvoidance(ForwardVector);

        // Use synchronized speed if in formation, otherwise use normal speed
        float EffectiveSpeed = bUseSynchronizedMovement ? SynchronizedMovementSpeed : MovementSpeed;
        float MovementDistance = EffectiveSpeed * DeltaTime;
        FVector MovementVector = AvoidanceDirection * MovementDistance;
        FVector NewLocation = CurrentLocation + MovementVector;

        // Move with minimal collision detection
        bool bMoved = SetActorLocation(NewLocation, false); // false = no collision sweep for now

        if (MovementCounter % 60 == 0) // Reduce log spam
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s MOVING FORWARD - Distance: %.2f, From: %s, To: %s, Moved: %s"),
                *GetName(), MovementDistance, *CurrentLocation.ToString(), *NewLocation.ToString(), bMoved ? TEXT("Yes") : TEXT("Blocked"));
        }

        if (!bMoved)
        {
            // Only try avoidance if enough time has passed since last attempt
            float CurrentTime = GetWorld()->GetTimeSeconds();
            if (CurrentTime - LastAvoidanceTime > AvoidanceCooldown)
            {
                // If blocked, try to turn slightly to find a path
                FRotator AvoidanceRotation = CurrentRotation;
                AvoidanceRotation.Yaw += 15.0f; // Turn 15 degrees to try to avoid obstacle
                SetActorRotation(AvoidanceRotation);
                LastAvoidanceTime = CurrentTime;

                if (MovementCounter % 60 == 0) // Reduce log spam
                {
                    UE_LOG(LogTemp, Warning, TEXT("RTSUnit: %s movement blocked, turning to avoid"), *GetName());
                }
            }
        }
    }
}

void ARTSUnit::UpdateCombat(float DeltaTime)
{
    if (WeaponController)
    {
        WeaponController->UpdateTargeting(DeltaTime);
    }
}



// AI Control Functions
void ARTSUnit::SetAIBehavior(ERTSAIBehavior Behavior)
{
    if (AIComponent)
    {
        AIComponent->SetBehavior(Behavior);
    }
}

void ARTSUnit::SetAIPatrolPoints(const TArray<FVector>& Points)
{
    if (ARTSAIController* AIController = Cast<ARTSAIController>(GetController()))
    {
        AIController->SetPatrolPoints(Points);
    }
}

void ARTSUnit::SetAIDefendPosition(const FVector& Position)
{
    if (ARTSAIController* AIController = Cast<ARTSAIController>(GetController()))
    {
        AIController->SetDefendPosition(Position);
    }
}

void ARTSUnit::JoinFormation(ARTSUnit* Leader, ERTSFormationRole FormationRole, const FVector& Offset)
{
    if (AIComponent)
    {
        AIComponent->JoinFormation(Leader, FormationRole, Offset);
    }
}

void ARTSUnit::LeaveFormation()
{
    if (AIComponent)
    {
        AIComponent->LeaveFormation();
    }
}

// Tech Level Functions
bool ARTSUnit::CanBeProducedByTechLevel(ERTSTechLevel FactoryTechLevel) const
{
    // Unit can be produced if factory tech level is equal or higher than unit's required tech level
    return static_cast<uint8>(FactoryTechLevel) >= static_cast<uint8>(GetTechLevel());
}
