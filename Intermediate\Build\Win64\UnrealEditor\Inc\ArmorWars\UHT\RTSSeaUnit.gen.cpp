// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSSeaUnit.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSSeaUnit() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSSeaUnit();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSSeaUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSSeaMovementComponent_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSSubmarineState();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSNavalFormationType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSNavalFormationType;
static UEnum* ERTSNavalFormationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSNavalFormationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSNavalFormationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSNavalFormationType"));
	}
	return Z_Registration_Info_UEnum_ERTSNavalFormationType.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSNavalFormationType>()
{
	return ERTSNavalFormationType_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BattleLine.DisplayName", "Battle Line" },
		{ "BattleLine.Name", "ERTSNavalFormationType::BattleLine" },
		{ "BlueprintType", "true" },
		{ "CircularScreen.DisplayName", "Circular Screen" },
		{ "CircularScreen.Name", "ERTSNavalFormationType::CircularScreen" },
		{ "Column.DisplayName", "Column" },
		{ "Column.Name", "ERTSNavalFormationType::Column" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum for naval formation types\n" },
#endif
		{ "Echelon.DisplayName", "Echelon" },
		{ "Echelon.Name", "ERTSNavalFormationType::Echelon" },
		{ "Line.DisplayName", "Line Ahead" },
		{ "Line.Name", "ERTSNavalFormationType::Line" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ERTSNavalFormationType::None" },
		{ "Screen.DisplayName", "Screen" },
		{ "Screen.Name", "ERTSNavalFormationType::Screen" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for naval formation types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSNavalFormationType::None", (int64)ERTSNavalFormationType::None },
		{ "ERTSNavalFormationType::Line", (int64)ERTSNavalFormationType::Line },
		{ "ERTSNavalFormationType::Column", (int64)ERTSNavalFormationType::Column },
		{ "ERTSNavalFormationType::Echelon", (int64)ERTSNavalFormationType::Echelon },
		{ "ERTSNavalFormationType::Screen", (int64)ERTSNavalFormationType::Screen },
		{ "ERTSNavalFormationType::CircularScreen", (int64)ERTSNavalFormationType::CircularScreen },
		{ "ERTSNavalFormationType::BattleLine", (int64)ERTSNavalFormationType::BattleLine },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSNavalFormationType",
	"ERTSNavalFormationType",
	Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType()
{
	if (!Z_Registration_Info_UEnum_ERTSNavalFormationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSNavalFormationType.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSNavalFormationType.InnerSingleton;
}
// ********** End Enum ERTSNavalFormationType ******************************************************

// ********** Begin Delegate FOnSubmarineStateChanged **********************************************
struct Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics
{
	struct RTSSeaUnit_eventOnSubmarineStateChanged_Parms
	{
		ARTSSeaUnit* SeaUnit;
		ERTSSubmarineState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SeaUnit;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::NewProp_SeaUnit = { "SeaUnit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventOnSubmarineStateChanged_Parms, SeaUnit), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventOnSubmarineStateChanged_Parms, NewState), Z_Construct_UEnum_ArmorWars_ERTSSubmarineState, METADATA_PARAMS(0, nullptr) }; // 3362524020
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::NewProp_SeaUnit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "OnSubmarineStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::RTSSeaUnit_eventOnSubmarineStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::RTSSeaUnit_eventOnSubmarineStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSSeaUnit::FOnSubmarineStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSubmarineStateChanged, ARTSSeaUnit* SeaUnit, ERTSSubmarineState NewState)
{
	struct RTSSeaUnit_eventOnSubmarineStateChanged_Parms
	{
		ARTSSeaUnit* SeaUnit;
		ERTSSubmarineState NewState;
	};
	RTSSeaUnit_eventOnSubmarineStateChanged_Parms Parms;
	Parms.SeaUnit=SeaUnit;
	Parms.NewState=NewState;
	OnSubmarineStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSubmarineStateChanged ************************************************

// ********** Begin Delegate FOnNavalFormationChanged **********************************************
struct Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics
{
	struct RTSSeaUnit_eventOnNavalFormationChanged_Parms
	{
		ARTSSeaUnit* SeaUnit;
		ERTSNavalFormationType Formation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SeaUnit;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Formation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Formation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::NewProp_SeaUnit = { "SeaUnit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventOnNavalFormationChanged_Parms, SeaUnit), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::NewProp_Formation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::NewProp_Formation = { "Formation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventOnNavalFormationChanged_Parms, Formation), Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType, METADATA_PARAMS(0, nullptr) }; // 2082689608
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::NewProp_SeaUnit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::NewProp_Formation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::NewProp_Formation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "OnNavalFormationChanged__DelegateSignature", Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::RTSSeaUnit_eventOnNavalFormationChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::RTSSeaUnit_eventOnNavalFormationChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSSeaUnit::FOnNavalFormationChanged_DelegateWrapper(const FMulticastScriptDelegate& OnNavalFormationChanged, ARTSSeaUnit* SeaUnit, ERTSNavalFormationType Formation)
{
	struct RTSSeaUnit_eventOnNavalFormationChanged_Parms
	{
		ARTSSeaUnit* SeaUnit;
		ERTSNavalFormationType Formation;
	};
	RTSSeaUnit_eventOnNavalFormationChanged_Parms Parms;
	Parms.SeaUnit=SeaUnit;
	Parms.Formation=Formation;
	OnNavalFormationChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnNavalFormationChanged ************************************************

// ********** Begin Delegate FOnNavalCombatEngaged *************************************************
struct Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics
{
	struct RTSSeaUnit_eventOnNavalCombatEngaged_Parms
	{
		ARTSSeaUnit* SeaUnit;
		ARTSUnit* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SeaUnit;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::NewProp_SeaUnit = { "SeaUnit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventOnNavalCombatEngaged_Parms, SeaUnit), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventOnNavalCombatEngaged_Parms, Target), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::NewProp_SeaUnit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "OnNavalCombatEngaged__DelegateSignature", Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::RTSSeaUnit_eventOnNavalCombatEngaged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::RTSSeaUnit_eventOnNavalCombatEngaged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSSeaUnit::FOnNavalCombatEngaged_DelegateWrapper(const FMulticastScriptDelegate& OnNavalCombatEngaged, ARTSSeaUnit* SeaUnit, ARTSUnit* Target)
{
	struct RTSSeaUnit_eventOnNavalCombatEngaged_Parms
	{
		ARTSSeaUnit* SeaUnit;
		ARTSUnit* Target;
	};
	RTSSeaUnit_eventOnNavalCombatEngaged_Parms Parms;
	Parms.SeaUnit=SeaUnit;
	Parms.Target=Target;
	OnNavalCombatEngaged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnNavalCombatEngaged ***************************************************

// ********** Begin Class ARTSSeaUnit Function CanDetectSubmarine **********************************
struct Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics
{
	struct RTSSeaUnit_eventCanDetectSubmarine_Parms
	{
		const ARTSSeaUnit* Submarine;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Sensors" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Submarine_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Submarine;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::NewProp_Submarine = { "Submarine", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventCanDetectSubmarine_Parms, Submarine), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Submarine_MetaData), NewProp_Submarine_MetaData) };
void Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSeaUnit_eventCanDetectSubmarine_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSeaUnit_eventCanDetectSubmarine_Parms), &Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::NewProp_Submarine,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "CanDetectSubmarine", Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::RTSSeaUnit_eventCanDetectSubmarine_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::RTSSeaUnit_eventCanDetectSubmarine_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execCanDetectSubmarine)
{
	P_GET_OBJECT(ARTSSeaUnit,Z_Param_Submarine);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanDetectSubmarine(Z_Param_Submarine);
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function CanDetectSubmarine ************************************

// ********** Begin Class ARTSSeaUnit Function CanDive *********************************************
struct Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics
{
	struct RTSSeaUnit_eventCanDive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Submarine" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSeaUnit_eventCanDive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSeaUnit_eventCanDive_Parms), &Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "CanDive", Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::RTSSeaUnit_eventCanDive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::RTSSeaUnit_eventCanDive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_CanDive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_CanDive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execCanDive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanDive();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function CanDive ***********************************************

// ********** Begin Class ARTSSeaUnit Function CanLaunchAircraft ***********************************
struct Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics
{
	struct RTSSeaUnit_eventCanLaunchAircraft_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Air Operations" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Air operations functions (for carriers)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Air operations functions (for carriers)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSeaUnit_eventCanLaunchAircraft_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSeaUnit_eventCanLaunchAircraft_Parms), &Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "CanLaunchAircraft", Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::RTSSeaUnit_eventCanLaunchAircraft_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::RTSSeaUnit_eventCanLaunchAircraft_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execCanLaunchAircraft)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanLaunchAircraft();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function CanLaunchAircraft *************************************

// ********** Begin Class ARTSSeaUnit Function Dive ************************************************
struct Z_Construct_UFunction_ARTSSeaUnit_Dive_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Submarine" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Submarine functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Submarine functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_Dive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "Dive", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_Dive_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_Dive_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSSeaUnit_Dive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_Dive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execDive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Dive();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function Dive **************************************************

// ********** Begin Class ARTSSeaUnit Function EngageSubmarine *************************************
struct Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics
{
	struct RTSSeaUnit_eventEngageSubmarine_Parms
	{
		ARTSSeaUnit* Submarine;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Combat" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Submarine;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::NewProp_Submarine = { "Submarine", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventEngageSubmarine_Parms, Submarine), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::NewProp_Submarine,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "EngageSubmarine", Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::RTSSeaUnit_eventEngageSubmarine_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::RTSSeaUnit_eventEngageSubmarine_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execEngageSubmarine)
{
	P_GET_OBJECT(ARTSSeaUnit,Z_Param_Submarine);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EngageSubmarine(Z_Param_Submarine);
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function EngageSubmarine ***************************************

// ********** Begin Class ARTSSeaUnit Function FindNearestSubmarine ********************************
struct Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics
{
	struct RTSSeaUnit_eventFindNearestSubmarine_Parms
	{
		ARTSSeaUnit* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Combat" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventFindNearestSubmarine_Parms, ReturnValue), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "FindNearestSubmarine", Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::RTSSeaUnit_eventFindNearestSubmarine_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::RTSSeaUnit_eventFindNearestSubmarine_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execFindNearestSubmarine)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSSeaUnit**)Z_Param__Result=P_THIS->FindNearestSubmarine();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function FindNearestSubmarine **********************************

// ********** Begin Class ARTSSeaUnit Function FireTorpedo *****************************************
struct Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics
{
	struct RTSSeaUnit_eventFireTorpedo_Parms
	{
		ARTSSeaUnit* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Naval combat functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Naval combat functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventFireTorpedo_Parms, Target), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "FireTorpedo", Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::RTSSeaUnit_eventFireTorpedo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::RTSSeaUnit_eventFireTorpedo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execFireTorpedo)
{
	P_GET_OBJECT(ARTSSeaUnit,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->FireTorpedo(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function FireTorpedo *******************************************

// ********** Begin Class ARTSSeaUnit Function GetDepthPercentage **********************************
struct Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics
{
	struct RTSSeaUnit_eventGetDepthPercentage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Submarine" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventGetDepthPercentage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "GetDepthPercentage", Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::RTSSeaUnit_eventGetDepthPercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::RTSSeaUnit_eventGetDepthPercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execGetDepthPercentage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetDepthPercentage();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function GetDepthPercentage ************************************

// ********** Begin Class ARTSSeaUnit Function GetRadarContacts ************************************
struct Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics
{
	struct RTSSeaUnit_eventGetRadarContacts_Parms
	{
		TArray<ARTSUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Sensors" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventGetRadarContacts_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "GetRadarContacts", Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::RTSSeaUnit_eventGetRadarContacts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::RTSSeaUnit_eventGetRadarContacts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execGetRadarContacts)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSUnit*>*)Z_Param__Result=P_THIS->GetRadarContacts();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function GetRadarContacts **************************************

// ********** Begin Class ARTSSeaUnit Function GetSonarContacts ************************************
struct Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics
{
	struct RTSSeaUnit_eventGetSonarContacts_Parms
	{
		TArray<ARTSSeaUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Sensors" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventGetSonarContacts_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "GetSonarContacts", Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::RTSSeaUnit_eventGetSonarContacts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::RTSSeaUnit_eventGetSonarContacts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execGetSonarContacts)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSSeaUnit*>*)Z_Param__Result=P_THIS->GetSonarContacts();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function GetSonarContacts **************************************

// ********** Begin Class ARTSSeaUnit Function GetSubmarinesInRange ********************************
struct Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics
{
	struct RTSSeaUnit_eventGetSubmarinesInRange_Parms
	{
		TArray<ARTSSeaUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Combat" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventGetSubmarinesInRange_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "GetSubmarinesInRange", Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::RTSSeaUnit_eventGetSubmarinesInRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::RTSSeaUnit_eventGetSubmarinesInRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execGetSubmarinesInRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSSeaUnit*>*)Z_Param__Result=P_THIS->GetSubmarinesInRange();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function GetSubmarinesInRange **********************************

// ********** Begin Class ARTSSeaUnit Function HasFuel *********************************************
struct Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics
{
	struct RTSSeaUnit_eventHasFuel_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Logistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simple fuel check (autonomous units don't need complex fuel management)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simple fuel check (autonomous units don't need complex fuel management)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSeaUnit_eventHasFuel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSeaUnit_eventHasFuel_Parms), &Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "HasFuel", Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::RTSSeaUnit_eventHasFuel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::RTSSeaUnit_eventHasFuel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_HasFuel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_HasFuel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execHasFuel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasFuel();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function HasFuel ***********************************************

// ********** Begin Class ARTSSeaUnit Function IsInNavalFormation **********************************
struct Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics
{
	struct RTSSeaUnit_eventIsInNavalFormation_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Formation" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSeaUnit_eventIsInNavalFormation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSeaUnit_eventIsInNavalFormation_Parms), &Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "IsInNavalFormation", Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::RTSSeaUnit_eventIsInNavalFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::RTSSeaUnit_eventIsInNavalFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execIsInNavalFormation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInNavalFormation();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function IsInNavalFormation ************************************

// ********** Begin Class ARTSSeaUnit Function IsNavalFormationLeader ******************************
struct Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics
{
	struct RTSSeaUnit_eventIsNavalFormationLeader_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Formation" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSeaUnit_eventIsNavalFormationLeader_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSeaUnit_eventIsNavalFormationLeader_Parms), &Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "IsNavalFormationLeader", Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::RTSSeaUnit_eventIsNavalFormationLeader_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::RTSSeaUnit_eventIsNavalFormationLeader_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execIsNavalFormationLeader)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsNavalFormationLeader();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function IsNavalFormationLeader ********************************

// ********** Begin Class ARTSSeaUnit Function IsSubmerged *****************************************
struct Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics
{
	struct RTSSeaUnit_eventIsSubmerged_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Submarine" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSeaUnit_eventIsSubmerged_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSeaUnit_eventIsSubmerged_Parms), &Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "IsSubmerged", Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::RTSSeaUnit_eventIsSubmerged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::RTSSeaUnit_eventIsSubmerged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execIsSubmerged)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSubmerged();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function IsSubmerged *******************************************

// ********** Begin Class ARTSSeaUnit Function JoinNavalFormation **********************************
struct Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics
{
	struct RTSSeaUnit_eventJoinNavalFormation_Parms
	{
		ARTSSeaUnit* Leader;
		ERTSNavalFormationType Formation;
		FVector Offset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Offset_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Leader;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Formation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Formation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Offset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::NewProp_Leader = { "Leader", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventJoinNavalFormation_Parms, Leader), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::NewProp_Formation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::NewProp_Formation = { "Formation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventJoinNavalFormation_Parms, Formation), Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType, METADATA_PARAMS(0, nullptr) }; // 2082689608
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventJoinNavalFormation_Parms, Offset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Offset_MetaData), NewProp_Offset_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::NewProp_Leader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::NewProp_Formation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::NewProp_Formation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::NewProp_Offset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "JoinNavalFormation", Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::RTSSeaUnit_eventJoinNavalFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::RTSSeaUnit_eventJoinNavalFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execJoinNavalFormation)
{
	P_GET_OBJECT(ARTSSeaUnit,Z_Param_Leader);
	P_GET_ENUM(ERTSNavalFormationType,Z_Param_Formation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Offset);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->JoinNavalFormation(Z_Param_Leader,ERTSNavalFormationType(Z_Param_Formation),Z_Param_Out_Offset);
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function JoinNavalFormation ************************************

// ********** Begin Class ARTSSeaUnit Function LeaveNavalFormation *********************************
struct Z_Construct_UFunction_ARTSSeaUnit_LeaveNavalFormation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Formation" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_LeaveNavalFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "LeaveNavalFormation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_LeaveNavalFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_LeaveNavalFormation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSSeaUnit_LeaveNavalFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_LeaveNavalFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execLeaveNavalFormation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LeaveNavalFormation();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function LeaveNavalFormation ***********************************

// ********** Begin Class ARTSSeaUnit Function OnDepthChanged **************************************
struct RTSSeaUnit_eventOnDepthChanged_Parms
{
	float NewDepth;
};
static FName NAME_ARTSSeaUnit_OnDepthChanged = FName(TEXT("OnDepthChanged"));
void ARTSSeaUnit::OnDepthChanged(float NewDepth)
{
	RTSSeaUnit_eventOnDepthChanged_Parms Parms;
	Parms.NewDepth=NewDepth;
	UFunction* Func = FindFunctionChecked(NAME_ARTSSeaUnit_OnDepthChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Events" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewDepth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged_Statics::NewProp_NewDepth = { "NewDepth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventOnDepthChanged_Parms, NewDepth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged_Statics::NewProp_NewDepth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "OnDepthChanged", Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged_Statics::PropPointers), sizeof(RTSSeaUnit_eventOnDepthChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSeaUnit_eventOnDepthChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSSeaUnit Function OnDepthChanged ****************************************

// ********** Begin Class ARTSSeaUnit Function OnFormationJoined ***********************************
struct RTSSeaUnit_eventOnFormationJoined_Parms
{
	ARTSSeaUnit* Leader;
};
static FName NAME_ARTSSeaUnit_OnFormationJoined = FName(TEXT("OnFormationJoined"));
void ARTSSeaUnit::OnFormationJoined(ARTSSeaUnit* Leader)
{
	RTSSeaUnit_eventOnFormationJoined_Parms Parms;
	Parms.Leader=Leader;
	UFunction* Func = FindFunctionChecked(NAME_ARTSSeaUnit_OnFormationJoined);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Events" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Leader;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined_Statics::NewProp_Leader = { "Leader", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventOnFormationJoined_Parms, Leader), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined_Statics::NewProp_Leader,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "OnFormationJoined", Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined_Statics::PropPointers), sizeof(RTSSeaUnit_eventOnFormationJoined_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSeaUnit_eventOnFormationJoined_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSSeaUnit Function OnFormationJoined *************************************

// ********** Begin Class ARTSSeaUnit Function OnFormationLeft *************************************
static FName NAME_ARTSSeaUnit_OnFormationLeft = FName(TEXT("OnFormationLeft"));
void ARTSSeaUnit::OnFormationLeft()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSSeaUnit_OnFormationLeft);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSSeaUnit_OnFormationLeft_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Events" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_OnFormationLeft_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "OnFormationLeft", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnFormationLeft_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_OnFormationLeft_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSSeaUnit_OnFormationLeft()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_OnFormationLeft_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSSeaUnit Function OnFormationLeft ***************************************

// ********** Begin Class ARTSSeaUnit Function OnSonarContact **************************************
struct RTSSeaUnit_eventOnSonarContact_Parms
{
	ARTSSeaUnit* Contact;
};
static FName NAME_ARTSSeaUnit_OnSonarContact = FName(TEXT("OnSonarContact"));
void ARTSSeaUnit::OnSonarContact(ARTSSeaUnit* Contact)
{
	RTSSeaUnit_eventOnSonarContact_Parms Parms;
	Parms.Contact=Contact;
	UFunction* Func = FindFunctionChecked(NAME_ARTSSeaUnit_OnSonarContact);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Events" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Contact;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact_Statics::NewProp_Contact = { "Contact", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventOnSonarContact_Parms, Contact), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact_Statics::NewProp_Contact,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "OnSonarContact", Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact_Statics::PropPointers), sizeof(RTSSeaUnit_eventOnSonarContact_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSeaUnit_eventOnSonarContact_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSSeaUnit Function OnSonarContact ****************************************

// ********** Begin Class ARTSSeaUnit Function OnSubmerged *****************************************
static FName NAME_ARTSSeaUnit_OnSubmerged = FName(TEXT("OnSubmerged"));
void ARTSSeaUnit::OnSubmerged()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSSeaUnit_OnSubmerged);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSSeaUnit_OnSubmerged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_OnSubmerged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "OnSubmerged", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnSubmerged_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_OnSubmerged_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSSeaUnit_OnSubmerged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_OnSubmerged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSSeaUnit Function OnSubmerged *******************************************

// ********** Begin Class ARTSSeaUnit Function OnSurfaced ******************************************
static FName NAME_ARTSSeaUnit_OnSurfaced = FName(TEXT("OnSurfaced"));
void ARTSSeaUnit::OnSurfaced()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSSeaUnit_OnSurfaced);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSSeaUnit_OnSurfaced_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Events" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_OnSurfaced_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "OnSurfaced", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnSurfaced_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_OnSurfaced_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSSeaUnit_OnSurfaced()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_OnSurfaced_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSSeaUnit Function OnSurfaced ********************************************

// ********** Begin Class ARTSSeaUnit Function OnTorpedoFired **************************************
struct RTSSeaUnit_eventOnTorpedoFired_Parms
{
	ARTSSeaUnit* Target;
};
static FName NAME_ARTSSeaUnit_OnTorpedoFired = FName(TEXT("OnTorpedoFired"));
void ARTSSeaUnit::OnTorpedoFired(ARTSSeaUnit* Target)
{
	RTSSeaUnit_eventOnTorpedoFired_Parms Parms;
	Parms.Target=Target;
	UFunction* Func = FindFunctionChecked(NAME_ARTSSeaUnit_OnTorpedoFired);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Events" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventOnTorpedoFired_Parms, Target), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "OnTorpedoFired", Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired_Statics::PropPointers), sizeof(RTSSeaUnit_eventOnTorpedoFired_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSeaUnit_eventOnTorpedoFired_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSSeaUnit Function OnTorpedoFired ****************************************

// ********** Begin Class ARTSSeaUnit Function SetDepth ********************************************
struct Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics
{
	struct RTSSeaUnit_eventSetDepth_Parms
	{
		float TargetDepth;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Submarine" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetDepth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::NewProp_TargetDepth = { "TargetDepth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventSetDepth_Parms, TargetDepth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::NewProp_TargetDepth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "SetDepth", Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::RTSSeaUnit_eventSetDepth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::RTSSeaUnit_eventSetDepth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_SetDepth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_SetDepth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execSetDepth)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_TargetDepth);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDepth(Z_Param_TargetDepth);
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function SetDepth **********************************************

// ********** Begin Class ARTSSeaUnit Function SetNavalFormationLeader *****************************
struct Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics
{
	struct RTSSeaUnit_eventSetNavalFormationLeader_Parms
	{
		TArray<ARTSSeaUnit*> Followers;
		ERTSNavalFormationType Formation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Formation" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Followers_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Followers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Followers;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Formation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Formation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::NewProp_Followers_Inner = { "Followers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::NewProp_Followers = { "Followers", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventSetNavalFormationLeader_Parms, Followers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Followers_MetaData), NewProp_Followers_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::NewProp_Formation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::NewProp_Formation = { "Formation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventSetNavalFormationLeader_Parms, Formation), Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType, METADATA_PARAMS(0, nullptr) }; // 2082689608
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::NewProp_Followers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::NewProp_Followers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::NewProp_Formation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::NewProp_Formation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "SetNavalFormationLeader", Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::RTSSeaUnit_eventSetNavalFormationLeader_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::RTSSeaUnit_eventSetNavalFormationLeader_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execSetNavalFormationLeader)
{
	P_GET_TARRAY_REF(ARTSSeaUnit*,Z_Param_Out_Followers);
	P_GET_ENUM(ERTSNavalFormationType,Z_Param_Formation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetNavalFormationLeader(Z_Param_Out_Followers,ERTSNavalFormationType(Z_Param_Formation));
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function SetNavalFormationLeader *******************************

// ********** Begin Class ARTSSeaUnit Function SetSubmarineState ***********************************
struct Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics
{
	struct RTSSeaUnit_eventSetSubmarineState_Parms
	{
		ERTSSubmarineState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Submarine" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSeaUnit_eventSetSubmarineState_Parms, NewState), Z_Construct_UEnum_ArmorWars_ERTSSubmarineState, METADATA_PARAMS(0, nullptr) }; // 3362524020
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "SetSubmarineState", Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::RTSSeaUnit_eventSetSubmarineState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::RTSSeaUnit_eventSetSubmarineState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execSetSubmarineState)
{
	P_GET_ENUM(ERTSSubmarineState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSubmarineState(ERTSSubmarineState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function SetSubmarineState *************************************

// ********** Begin Class ARTSSeaUnit Function Surface *********************************************
struct Z_Construct_UFunction_ARTSSeaUnit_Surface_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Submarine" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_Surface_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "Surface", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_Surface_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_Surface_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSSeaUnit_Surface()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_Surface_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execSurface)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Surface();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function Surface ***********************************************

// ********** Begin Class ARTSSeaUnit Function ToggleRadar *****************************************
struct Z_Construct_UFunction_ARTSSeaUnit_ToggleRadar_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Sensors" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_ToggleRadar_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "ToggleRadar", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_ToggleRadar_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_ToggleRadar_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSSeaUnit_ToggleRadar()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_ToggleRadar_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execToggleRadar)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ToggleRadar();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function ToggleRadar *******************************************

// ********** Begin Class ARTSSeaUnit Function ToggleSonar *****************************************
struct Z_Construct_UFunction_ARTSSeaUnit_ToggleSonar_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sea Unit|Sensors" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sensor functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sensor functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSSeaUnit_ToggleSonar_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSSeaUnit, nullptr, "ToggleSonar", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSSeaUnit_ToggleSonar_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSSeaUnit_ToggleSonar_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSSeaUnit_ToggleSonar()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSSeaUnit_ToggleSonar_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSSeaUnit::execToggleSonar)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ToggleSonar();
	P_NATIVE_END;
}
// ********** End Class ARTSSeaUnit Function ToggleSonar *******************************************

// ********** Begin Class ARTSSeaUnit **************************************************************
void ARTSSeaUnit::StaticRegisterNativesARTSSeaUnit()
{
	UClass* Class = ARTSSeaUnit::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanDetectSubmarine", &ARTSSeaUnit::execCanDetectSubmarine },
		{ "CanDive", &ARTSSeaUnit::execCanDive },
		{ "CanLaunchAircraft", &ARTSSeaUnit::execCanLaunchAircraft },
		{ "Dive", &ARTSSeaUnit::execDive },
		{ "EngageSubmarine", &ARTSSeaUnit::execEngageSubmarine },
		{ "FindNearestSubmarine", &ARTSSeaUnit::execFindNearestSubmarine },
		{ "FireTorpedo", &ARTSSeaUnit::execFireTorpedo },
		{ "GetDepthPercentage", &ARTSSeaUnit::execGetDepthPercentage },
		{ "GetRadarContacts", &ARTSSeaUnit::execGetRadarContacts },
		{ "GetSonarContacts", &ARTSSeaUnit::execGetSonarContacts },
		{ "GetSubmarinesInRange", &ARTSSeaUnit::execGetSubmarinesInRange },
		{ "HasFuel", &ARTSSeaUnit::execHasFuel },
		{ "IsInNavalFormation", &ARTSSeaUnit::execIsInNavalFormation },
		{ "IsNavalFormationLeader", &ARTSSeaUnit::execIsNavalFormationLeader },
		{ "IsSubmerged", &ARTSSeaUnit::execIsSubmerged },
		{ "JoinNavalFormation", &ARTSSeaUnit::execJoinNavalFormation },
		{ "LeaveNavalFormation", &ARTSSeaUnit::execLeaveNavalFormation },
		{ "SetDepth", &ARTSSeaUnit::execSetDepth },
		{ "SetNavalFormationLeader", &ARTSSeaUnit::execSetNavalFormationLeader },
		{ "SetSubmarineState", &ARTSSeaUnit::execSetSubmarineState },
		{ "Surface", &ARTSSeaUnit::execSurface },
		{ "ToggleRadar", &ARTSSeaUnit::execToggleRadar },
		{ "ToggleSonar", &ARTSSeaUnit::execToggleSonar },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ARTSSeaUnit;
UClass* ARTSSeaUnit::GetPrivateStaticClass()
{
	using TClass = ARTSSeaUnit;
	if (!Z_Registration_Info_UClass_ARTSSeaUnit.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSSeaUnit"),
			Z_Registration_Info_UClass_ARTSSeaUnit.InnerSingleton,
			StaticRegisterNativesARTSSeaUnit,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ARTSSeaUnit.InnerSingleton;
}
UClass* Z_Construct_UClass_ARTSSeaUnit_NoRegister()
{
	return ARTSSeaUnit::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ARTSSeaUnit_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Specialized sea unit class with naval movement and water-specific features\n * Inherits from RTSUnit and adds naval combat, formations, and submarine mechanics\n */" },
#endif
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "RTSSeaUnit.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Specialized sea unit class with naval movement and water-specific features\nInherits from RTSUnit and adds naval combat, formations, and submarine mechanics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentDepth_MetaData[] = {
		{ "Category", "Sea Unit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current depth (negative values for underwater)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current depth (negative values for underwater)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDivingDepth_MetaData[] = {
		{ "Category", "Submarine" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Maximum diving depth (for submarines)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum diving depth (for submarines)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubmarineState_MetaData[] = {
		{ "Category", "Submarine" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Submarine state (only relevant for submarines)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Submarine state (only relevant for submarines)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanSubmerge_MetaData[] = {
		{ "Category", "Sea Unit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this unit can submerge\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this unit can submerge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationType_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Naval formation\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Naval formation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationLeader_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation leader\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation leader" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SonarRange_MetaData[] = {
		{ "Category", "Sensors" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sonar range\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sonar range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RadarRange_MetaData[] = {
		{ "Category", "Sensors" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Radar range (surface only)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Radar range (surface only)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSonarActive_MetaData[] = {
		{ "Category", "Sensors" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether sonar is active\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether sonar is active" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRadarActive_MetaData[] = {
		{ "Category", "Sensors" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether radar is active\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether radar is active" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TorpedoRange_MetaData[] = {
		{ "Category", "Naval Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Torpedo range (for submarines and destroyers)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Torpedo range (for submarines and destroyers)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ASWRange_MetaData[] = {
		{ "Category", "Naval Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Anti-submarine warfare range\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Anti-submarine warfare range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanEngageSubmarines_MetaData[] = {
		{ "Category", "Naval Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this unit can engage submarines\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this unit can engage submarines" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanLaunchAircraft_MetaData[] = {
		{ "Category", "Air Operations" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this unit can launch aircraft (carriers)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this unit can launch aircraft (carriers)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeaMovementComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSubmarineStateChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnNavalFormationChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnNavalCombatEngaged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSSeaUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentDepth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDivingDepth;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SubmarineState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SubmarineState;
	static void NewProp_bCanSubmerge_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanSubmerge;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FormationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FormationType;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_FormationLeader;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SonarRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RadarRange;
	static void NewProp_bSonarActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSonarActive;
	static void NewProp_bRadarActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRadarActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TorpedoRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ASWRange;
	static void NewProp_bCanEngageSubmarines_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanEngageSubmarines;
	static void NewProp_bCanLaunchAircraft_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanLaunchAircraft;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SeaMovementComponent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSubmarineStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnNavalFormationChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnNavalCombatEngaged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARTSSeaUnit_CanDetectSubmarine, "CanDetectSubmarine" }, // 4022699097
		{ &Z_Construct_UFunction_ARTSSeaUnit_CanDive, "CanDive" }, // 3661569982
		{ &Z_Construct_UFunction_ARTSSeaUnit_CanLaunchAircraft, "CanLaunchAircraft" }, // 356019316
		{ &Z_Construct_UFunction_ARTSSeaUnit_Dive, "Dive" }, // 1602478332
		{ &Z_Construct_UFunction_ARTSSeaUnit_EngageSubmarine, "EngageSubmarine" }, // 3577557698
		{ &Z_Construct_UFunction_ARTSSeaUnit_FindNearestSubmarine, "FindNearestSubmarine" }, // 2837004161
		{ &Z_Construct_UFunction_ARTSSeaUnit_FireTorpedo, "FireTorpedo" }, // 1857603749
		{ &Z_Construct_UFunction_ARTSSeaUnit_GetDepthPercentage, "GetDepthPercentage" }, // 1415869219
		{ &Z_Construct_UFunction_ARTSSeaUnit_GetRadarContacts, "GetRadarContacts" }, // 2285157996
		{ &Z_Construct_UFunction_ARTSSeaUnit_GetSonarContacts, "GetSonarContacts" }, // 423144125
		{ &Z_Construct_UFunction_ARTSSeaUnit_GetSubmarinesInRange, "GetSubmarinesInRange" }, // 1621764424
		{ &Z_Construct_UFunction_ARTSSeaUnit_HasFuel, "HasFuel" }, // 2204963453
		{ &Z_Construct_UFunction_ARTSSeaUnit_IsInNavalFormation, "IsInNavalFormation" }, // 1843384461
		{ &Z_Construct_UFunction_ARTSSeaUnit_IsNavalFormationLeader, "IsNavalFormationLeader" }, // 3786613203
		{ &Z_Construct_UFunction_ARTSSeaUnit_IsSubmerged, "IsSubmerged" }, // 3153408563
		{ &Z_Construct_UFunction_ARTSSeaUnit_JoinNavalFormation, "JoinNavalFormation" }, // 3888208293
		{ &Z_Construct_UFunction_ARTSSeaUnit_LeaveNavalFormation, "LeaveNavalFormation" }, // 562163132
		{ &Z_Construct_UFunction_ARTSSeaUnit_OnDepthChanged, "OnDepthChanged" }, // 1164974599
		{ &Z_Construct_UFunction_ARTSSeaUnit_OnFormationJoined, "OnFormationJoined" }, // 3924057124
		{ &Z_Construct_UFunction_ARTSSeaUnit_OnFormationLeft, "OnFormationLeft" }, // 3601823587
		{ &Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature, "OnNavalCombatEngaged__DelegateSignature" }, // 2241558386
		{ &Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature, "OnNavalFormationChanged__DelegateSignature" }, // 2427031580
		{ &Z_Construct_UFunction_ARTSSeaUnit_OnSonarContact, "OnSonarContact" }, // 1848734329
		{ &Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature, "OnSubmarineStateChanged__DelegateSignature" }, // 3060717393
		{ &Z_Construct_UFunction_ARTSSeaUnit_OnSubmerged, "OnSubmerged" }, // 2609559448
		{ &Z_Construct_UFunction_ARTSSeaUnit_OnSurfaced, "OnSurfaced" }, // 615544415
		{ &Z_Construct_UFunction_ARTSSeaUnit_OnTorpedoFired, "OnTorpedoFired" }, // 990428746
		{ &Z_Construct_UFunction_ARTSSeaUnit_SetDepth, "SetDepth" }, // 530874710
		{ &Z_Construct_UFunction_ARTSSeaUnit_SetNavalFormationLeader, "SetNavalFormationLeader" }, // 2592843944
		{ &Z_Construct_UFunction_ARTSSeaUnit_SetSubmarineState, "SetSubmarineState" }, // 2757687715
		{ &Z_Construct_UFunction_ARTSSeaUnit_Surface, "Surface" }, // 2815551006
		{ &Z_Construct_UFunction_ARTSSeaUnit_ToggleRadar, "ToggleRadar" }, // 844540171
		{ &Z_Construct_UFunction_ARTSSeaUnit_ToggleSonar, "ToggleSonar" }, // 2992908299
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARTSSeaUnit>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_CurrentDepth = { "CurrentDepth", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, CurrentDepth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentDepth_MetaData), NewProp_CurrentDepth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_MaxDivingDepth = { "MaxDivingDepth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, MaxDivingDepth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDivingDepth_MetaData), NewProp_MaxDivingDepth_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_SubmarineState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_SubmarineState = { "SubmarineState", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, SubmarineState), Z_Construct_UEnum_ArmorWars_ERTSSubmarineState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubmarineState_MetaData), NewProp_SubmarineState_MetaData) }; // 3362524020
void Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanSubmerge_SetBit(void* Obj)
{
	((ARTSSeaUnit*)Obj)->bCanSubmerge = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanSubmerge = { "bCanSubmerge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSSeaUnit), &Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanSubmerge_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanSubmerge_MetaData), NewProp_bCanSubmerge_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_FormationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_FormationType = { "FormationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, FormationType), Z_Construct_UEnum_ArmorWars_ERTSNavalFormationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationType_MetaData), NewProp_FormationType_MetaData) }; // 2082689608
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_FormationLeader = { "FormationLeader", nullptr, (EPropertyFlags)0x0014000000000004, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, FormationLeader), Z_Construct_UClass_ARTSSeaUnit_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationLeader_MetaData), NewProp_FormationLeader_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_SonarRange = { "SonarRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, SonarRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SonarRange_MetaData), NewProp_SonarRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_RadarRange = { "RadarRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, RadarRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RadarRange_MetaData), NewProp_RadarRange_MetaData) };
void Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bSonarActive_SetBit(void* Obj)
{
	((ARTSSeaUnit*)Obj)->bSonarActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bSonarActive = { "bSonarActive", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSSeaUnit), &Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bSonarActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSonarActive_MetaData), NewProp_bSonarActive_MetaData) };
void Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bRadarActive_SetBit(void* Obj)
{
	((ARTSSeaUnit*)Obj)->bRadarActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bRadarActive = { "bRadarActive", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSSeaUnit), &Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bRadarActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRadarActive_MetaData), NewProp_bRadarActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_TorpedoRange = { "TorpedoRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, TorpedoRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TorpedoRange_MetaData), NewProp_TorpedoRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_ASWRange = { "ASWRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, ASWRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ASWRange_MetaData), NewProp_ASWRange_MetaData) };
void Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanEngageSubmarines_SetBit(void* Obj)
{
	((ARTSSeaUnit*)Obj)->bCanEngageSubmarines = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanEngageSubmarines = { "bCanEngageSubmarines", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSSeaUnit), &Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanEngageSubmarines_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanEngageSubmarines_MetaData), NewProp_bCanEngageSubmarines_MetaData) };
void Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanLaunchAircraft_SetBit(void* Obj)
{
	((ARTSSeaUnit*)Obj)->bCanLaunchAircraft = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanLaunchAircraft = { "bCanLaunchAircraft", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSSeaUnit), &Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanLaunchAircraft_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanLaunchAircraft_MetaData), NewProp_bCanLaunchAircraft_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_SeaMovementComponent = { "SeaMovementComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, SeaMovementComponent), Z_Construct_UClass_URTSSeaMovementComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeaMovementComponent_MetaData), NewProp_SeaMovementComponent_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_OnSubmarineStateChanged = { "OnSubmarineStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, OnSubmarineStateChanged), Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSubmarineStateChanged_MetaData), NewProp_OnSubmarineStateChanged_MetaData) }; // 3060717393
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_OnNavalFormationChanged = { "OnNavalFormationChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, OnNavalFormationChanged), Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnNavalFormationChanged_MetaData), NewProp_OnNavalFormationChanged_MetaData) }; // 2427031580
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_OnNavalCombatEngaged = { "OnNavalCombatEngaged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSSeaUnit, OnNavalCombatEngaged), Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnNavalCombatEngaged_MetaData), NewProp_OnNavalCombatEngaged_MetaData) }; // 2241558386
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARTSSeaUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_CurrentDepth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_MaxDivingDepth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_SubmarineState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_SubmarineState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanSubmerge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_FormationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_FormationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_FormationLeader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_SonarRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_RadarRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bSonarActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bRadarActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_TorpedoRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_ASWRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanEngageSubmarines,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_bCanLaunchAircraft,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_SeaMovementComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_OnSubmarineStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_OnNavalFormationChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSSeaUnit_Statics::NewProp_OnNavalCombatEngaged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSSeaUnit_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARTSSeaUnit_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ARTSUnit,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSSeaUnit_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARTSSeaUnit_Statics::ClassParams = {
	&ARTSSeaUnit::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARTSSeaUnit_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARTSSeaUnit_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSSeaUnit_Statics::Class_MetaDataParams), Z_Construct_UClass_ARTSSeaUnit_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARTSSeaUnit()
{
	if (!Z_Registration_Info_UClass_ARTSSeaUnit.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARTSSeaUnit.OuterSingleton, Z_Construct_UClass_ARTSSeaUnit_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARTSSeaUnit.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARTSSeaUnit);
ARTSSeaUnit::~ARTSSeaUnit() {}
// ********** End Class ARTSSeaUnit ****************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSeaUnit_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSNavalFormationType_StaticEnum, TEXT("ERTSNavalFormationType"), &Z_Registration_Info_UEnum_ERTSNavalFormationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2082689608U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARTSSeaUnit, ARTSSeaUnit::StaticClass, TEXT("ARTSSeaUnit"), &Z_Registration_Info_UClass_ARTSSeaUnit, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARTSSeaUnit), 1269097667U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSeaUnit_h__Script_ArmorWars_1777325578(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSeaUnit_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSeaUnit_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSeaUnit_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSeaUnit_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
