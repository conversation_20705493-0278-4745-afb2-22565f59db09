// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSUnit.h"

#ifdef ARMORWARS_RTSUnit_generated_h
#error "RTSUnit.generated.h already included, missing '#pragma once' in RTSUnit.h"
#endif
#define ARMORWARS_RTSUnit_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class ARTSBaseActor;
class ARTSUnit;
enum class ERTSAIBehavior : uint8;
enum class ERTSFormationRole : uint8;
enum class ERTSMovementState : uint8;
enum class ERTSTechLevel : uint8;
enum class ERTSUnitDomain : uint8;

// ********** Begin Delegate FOnHealthChanged ******************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_31_DELEGATE \
ARMORWARS_API void FOnHealthChanged_DelegateWrapper(const FMulticastScriptDelegate& OnHealthChanged, float CurrentHealth, float MaxHealth);


// ********** End Delegate FOnHealthChanged ********************************************************

// ********** Begin Delegate FOnDamageReceived *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_32_DELEGATE \
ARMORWARS_API void FOnDamageReceived_DelegateWrapper(const FMulticastScriptDelegate& OnDamageReceived, float DamageAmount, AActor* DamageSource);


// ********** End Delegate FOnDamageReceived *******************************************************

// ********** Begin Delegate FOnActorDeath *********************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_33_DELEGATE \
ARMORWARS_API void FOnActorDeath_DelegateWrapper(const FMulticastScriptDelegate& OnActorDeath, AActor* DeadActor);


// ********** End Delegate FOnActorDeath ***********************************************************

// ********** Begin Delegate FOnUnitSelectionChanged ***********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_34_DELEGATE \
ARMORWARS_API void FOnUnitSelectionChanged_DelegateWrapper(const FMulticastScriptDelegate& OnUnitSelectionChanged, AActor* Actor, bool bSelected);


// ********** End Delegate FOnUnitSelectionChanged *************************************************

// ********** Begin Delegate FOnMovementChanged ****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_35_DELEGATE \
ARMORWARS_API void FOnMovementChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMovementChanged, ARTSUnit* Unit, FVector TargetLocation);


// ********** End Delegate FOnMovementChanged ******************************************************

// ********** Begin Class ARTSUnit *****************************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_45_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLeaveFormation); \
	DECLARE_FUNCTION(execJoinFormation); \
	DECLARE_FUNCTION(execSetAIDefendPosition); \
	DECLARE_FUNCTION(execSetAIPatrolPoints); \
	DECLARE_FUNCTION(execSetAIBehavior); \
	DECLARE_FUNCTION(execIsAircraft); \
	DECLARE_FUNCTION(execFindEnemiesInRange); \
	DECLARE_FUNCTION(execGetAttackDamage); \
	DECLARE_FUNCTION(execGetAttackRange); \
	DECLARE_FUNCTION(execCanTargetDomain); \
	DECLARE_FUNCTION(execGetTotalDamagePerSecond); \
	DECLARE_FUNCTION(execGetMaxAttackRange); \
	DECLARE_FUNCTION(execGetCurrentTarget); \
	DECLARE_FUNCTION(execIsAttacking); \
	DECLARE_FUNCTION(execIsInAttackRange); \
	DECLARE_FUNCTION(execCanAttackTarget); \
	DECLARE_FUNCTION(execStopAttacking); \
	DECLARE_FUNCTION(execAttackTarget); \
	DECLARE_FUNCTION(execCanBeProducedByTechLevel); \
	DECLARE_FUNCTION(execIsAdvancedUnit); \
	DECLARE_FUNCTION(execSetTechLevel); \
	DECLARE_FUNCTION(execGetTechLevel); \
	DECLARE_FUNCTION(execGetUnitDomain); \
	DECLARE_FUNCTION(execIsSubnauticalUnit); \
	DECLARE_FUNCTION(execIsSeaUnit); \
	DECLARE_FUNCTION(execIsAirUnit); \
	DECLARE_FUNCTION(execIsLandUnit); \
	DECLARE_FUNCTION(execGetNearbyUnits); \
	DECLARE_FUNCTION(execCalculateCollisionAvoidance); \
	DECLARE_FUNCTION(execGetFormationOffset); \
	DECLARE_FUNCTION(execIsInFormation); \
	DECLARE_FUNCTION(execSetFormationData); \
	DECLARE_FUNCTION(execMoveToLocationWithFormation); \
	DECLARE_FUNCTION(execSetMovementState); \
	DECLARE_FUNCTION(execGetMovementState); \
	DECLARE_FUNCTION(execGetDistanceToTarget); \
	DECLARE_FUNCTION(execGetMovementSpeed); \
	DECLARE_FUNCTION(execIsMoving); \
	DECLARE_FUNCTION(execAttackMoveToLocation); \
	DECLARE_FUNCTION(execStopMovement); \
	DECLARE_FUNCTION(execMoveToLocationSynchronized); \
	DECLARE_FUNCTION(execMoveToLocation); \
	DECLARE_FUNCTION(execSetSelected); \
	DECLARE_FUNCTION(execIsSelected); \
	DECLARE_FUNCTION(execIsEnemy); \
	DECLARE_FUNCTION(execIsOnSameTeam); \
	DECLARE_FUNCTION(execSetTeamID); \
	DECLARE_FUNCTION(execGetTeamID); \
	DECLARE_FUNCTION(execSetHealth); \
	DECLARE_FUNCTION(execHeal); \
	DECLARE_FUNCTION(execTakeDamageSimple); \
	DECLARE_FUNCTION(execIsDead); \
	DECLARE_FUNCTION(execIsAlive); \
	DECLARE_FUNCTION(execGetHealthPercentage); \
	DECLARE_FUNCTION(execGetMaxHealth); \
	DECLARE_FUNCTION(execGetHealth);


#define FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_45_CALLBACK_WRAPPERS
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_45_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesARTSUnit(); \
	friend struct Z_Construct_UClass_ARTSUnit_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister(); \
public: \
	DECLARE_CLASS2(ARTSUnit, APawn, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_ARTSUnit_NoRegister) \
	DECLARE_SERIALIZER(ARTSUnit)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_45_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ARTSUnit(ARTSUnit&&) = delete; \
	ARTSUnit(const ARTSUnit&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ARTSUnit); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ARTSUnit); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ARTSUnit) \
	NO_API virtual ~ARTSUnit();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_42_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_45_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_45_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_45_CALLBACK_WRAPPERS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_45_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h_45_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ARTSUnit;

// ********** End Class ARTSUnit *******************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h

// ********** Begin Enum ERTSMovementState *********************************************************
#define FOREACH_ENUM_ERTSMOVEMENTSTATE(op) \
	op(ERTSMovementState::Stationary) \
	op(ERTSMovementState::TurningInPlace) \
	op(ERTSMovementState::Moving) \
	op(ERTSMovementState::TurningWhileMoving) 

enum class ERTSMovementState : uint8;
template<> struct TIsUEnumClass<ERTSMovementState> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSMovementState>();
// ********** End Enum ERTSMovementState ***********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
