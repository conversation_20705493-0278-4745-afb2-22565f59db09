#include "Components/RTSInterfaceComponent.h"
#include "RTSPlayerController.h"
#include "RTSGameInstance.h"
#include "RTSHUD.h"
#include "RTSBaseActor.h"

#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"

URTSInterfaceComponent::URTSInterfaceComponent()
{
    PrimaryComponentTick.bCanEverTick = false;
}

void URTSInterfaceComponent::BeginPlay()
{
    Super::BeginPlay();
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: BeginPlay called on %s"), 
            GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
    }
}

// === Game Instance Interface Wrappers ===

void URTSInterfaceComponent::InitializeRTSGame()
{
    if (auto Interface = GetGameInstanceInterface())
    {
        Interface->InitializeRTSGame();
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: InitializeRTSGame called"));
        }
    }
}

void URTSInterfaceComponent::NotifyPlayerJoined(ARTSPlayerController* PlayerController)
{
    if (auto Interface = GetGameInstanceInterface())
    {
        Interface->OnPlayerJoinedGame(PlayerController);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Player joined - %s"), 
                PlayerController ? *PlayerController->GetName() : TEXT("None"));
        }
    }
}

void URTSInterfaceComponent::NotifyPlayerLeft(ARTSPlayerController* PlayerController)
{
    if (auto Interface = GetGameInstanceInterface())
    {
        Interface->OnPlayerLeftGame(PlayerController);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Player left - %s"), 
                PlayerController ? *PlayerController->GetName() : TEXT("None"));
        }
    }
}

void URTSInterfaceComponent::StartMatch()
{
    if (auto Interface = GetGameInstanceInterface())
    {
        Interface->StartMatch();
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: StartMatch called"));
        }
    }
}

void URTSInterfaceComponent::EndMatch(int32 WinningTeamID)
{
    if (auto Interface = GetGameInstanceInterface())
    {
        Interface->EndMatch(WinningTeamID);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: EndMatch called - Winner: Team %d"), WinningTeamID);
        }
    }
}

// === Pawn Interface Wrappers ===

ERTSPawnType URTSInterfaceComponent::GetPawnType(AActor* TargetActor)
{
    if (auto Interface = GetPawnInterface(TargetActor))
    {
        return Interface->GetPawnType();
    }
    return ERTSPawnType::Other;
}

ERTSPawnState URTSInterfaceComponent::GetPawnState(AActor* TargetActor)
{
    if (auto Interface = GetPawnInterface(TargetActor))
    {
        return Interface->GetPawnState();
    }
    return ERTSPawnState::Idle;
}

void URTSInterfaceComponent::SetPawnState(AActor* TargetActor, ERTSPawnState NewState)
{
    if (auto Interface = GetPawnInterface(TargetActor))
    {
        Interface->SetPawnState(NewState);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Set pawn state to %s for %s"), 
                *UEnum::GetValueAsString(NewState), TargetActor ? *TargetActor->GetName() : TEXT("None"));
        }
    }
}

bool URTSInterfaceComponent::IsPawnAlive(AActor* TargetActor)
{
    if (auto Interface = GetPawnInterface(TargetActor))
    {
        return Interface->IsAlive();
    }
    return false;
}

void URTSInterfaceComponent::SetPawnSelected(AActor* TargetActor, bool bSelected)
{
    if (auto Interface = GetPawnInterface(TargetActor))
    {
        Interface->SetSelected(bSelected);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Set selection %s for %s"), 
                bSelected ? TEXT("true") : TEXT("false"), TargetActor ? *TargetActor->GetName() : TEXT("None"));
        }
    }
}

float URTSInterfaceComponent::GetPawnHealthPercentage(AActor* TargetActor)
{
    if (auto Interface = GetPawnInterface(TargetActor))
    {
        return Interface->GetHealthPercentage();
    }
    return 0.0f;
}

// === HUD Interface Wrappers ===

void URTSInterfaceComponent::UpdateResourceDisplay(const FRTSResourceInfo& ResourceInfo)
{
    if (auto Interface = GetHUDInterface())
    {
        Interface->UpdateResourceDisplay(ResourceInfo);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("RTSInterfaceComponent: Updated resource display - Power: %.2f, Matter: %.2f"), 
                ResourceInfo.Power, ResourceInfo.Matter);
        }
    }
}

void URTSInterfaceComponent::SetHUDElementVisibility(ERTSHUDElement Element, bool bVisible)
{
    if (auto Interface = GetHUDInterface())
    {
        Interface->SetHUDElementVisibility(Element, bVisible);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Set HUD element %s visibility to %s"), 
                *UEnum::GetValueAsString(Element), bVisible ? TEXT("visible") : TEXT("hidden"));
        }
    }
}

void URTSInterfaceComponent::UpdateUnitInfo(const TArray<ARTSBaseActor*>& SelectedUnits)
{
    if (auto Interface = GetHUDInterface())
    {
        Interface->UpdateUnitInfo(SelectedUnits);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Updated unit info for %d units"), SelectedUnits.Num());
        }
    }
}

void URTSInterfaceComponent::ShowNotification(const FString& Message, float Duration, bool bIsWarning)
{
    if (auto Interface = GetHUDInterface())
    {
        Interface->ShowNotification(Message, Duration, bIsWarning);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Showing notification: %s"), *Message);
        }
    }
}

void URTSInterfaceComponent::ShowSelectionBox(const FVector2D& StartPosition, const FVector2D& EndPosition)
{
    if (auto Interface = GetHUDInterface())
    {
        Interface->ShowSelectionBox(StartPosition, EndPosition);
    }
}

void URTSInterfaceComponent::HideSelectionBox()
{
    if (auto Interface = GetHUDInterface())
    {
        Interface->HideSelectionBox();
    }
}

// === Unit Selection Interface Wrappers ===

bool URTSInterfaceComponent::SelectUnit(ARTSBaseActor* Unit, bool bAddToSelection)
{
    if (auto Interface = GetSelectionInterface())
    {
        bool Result = Interface->SelectUnit(Unit, bAddToSelection);
        
        if (bEnableDebugLogging && Result)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Selected unit %s"), 
                Unit ? *Unit->GetName() : TEXT("None"));
        }
        
        return Result;
    }
    return false;
}

bool URTSInterfaceComponent::DeselectUnit(ARTSBaseActor* Unit)
{
    if (auto Interface = GetSelectionInterface())
    {
        bool Result = Interface->DeselectUnit(Unit);
        
        if (bEnableDebugLogging && Result)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Deselected unit %s"), 
                Unit ? *Unit->GetName() : TEXT("None"));
        }
        
        return Result;
    }
    return false;
}

int32 URTSInterfaceComponent::SelectMultipleUnits(const TArray<ARTSBaseActor*>& Units, bool bAddToSelection)
{
    if (auto Interface = GetSelectionInterface())
    {
        int32 Result = Interface->SelectUnits(Units, bAddToSelection);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Selected %d out of %d units"), Result, Units.Num());
        }
        
        return Result;
    }
    return 0;
}

void URTSInterfaceComponent::ClearSelection()
{
    if (auto Interface = GetSelectionInterface())
    {
        Interface->ClearSelection();
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Cleared selection"));
        }
    }
}

TArray<ARTSBaseActor*> URTSInterfaceComponent::GetSelectedUnits()
{
    if (auto Interface = GetSelectionInterface())
    {
        return Interface->GetSelectedUnits();
    }
    return TArray<ARTSBaseActor*>();
}

bool URTSInterfaceComponent::SaveSelectionGroup(int32 GroupIndex)
{
    if (auto Interface = GetSelectionInterface())
    {
        bool Result = Interface->SaveSelectionGroup(GroupIndex);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: %s selection group %d"), 
                Result ? TEXT("Saved") : TEXT("Failed to save"), GroupIndex);
        }
        
        return Result;
    }
    return false;
}

bool URTSInterfaceComponent::LoadSelectionGroup(int32 GroupIndex, bool bAddToSelection)
{
    if (auto Interface = GetSelectionInterface())
    {
        bool Result = Interface->LoadSelectionGroup(GroupIndex, bAddToSelection);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: %s selection group %d"), 
                Result ? TEXT("Loaded") : TEXT("Failed to load"), GroupIndex);
        }
        
        return Result;
    }
    return false;
}

int32 URTSInterfaceComponent::SelectUnitsInBox(const FVector2D& StartPosition, const FVector2D& EndPosition, ERTSSelectionFilter Filter)
{
    if (auto Interface = GetSelectionInterface())
    {
        int32 Result = Interface->SelectUnitsInBox(StartPosition, EndPosition, Filter);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSInterfaceComponent: Selected %d units in box"), Result);
        }
        
        return Result;
    }
    return 0;
}

// === Utility Functions ===

TScriptInterface<IRTSGameInstanceInterface> URTSInterfaceComponent::GetGameInstanceInterface()
{
    if (UWorld* World = GetWorld())
    {
        if (UGameInstance* GameInstance = World->GetGameInstance())
        {
            if (GameInstance->Implements<URTSGameInstanceInterface>())
            {
                return TScriptInterface<IRTSGameInstanceInterface>(GameInstance);
            }
        }
    }
    return TScriptInterface<IRTSGameInstanceInterface>();
}

TScriptInterface<IRTSHUDInterface> URTSInterfaceComponent::GetHUDInterface(ARTSPlayerController* PlayerController)
{
    ARTSPlayerController* PC = PlayerController ? PlayerController : GetRTSPlayerController();
    if (PC)
    {
        if (AHUD* HUD = PC->GetHUD())
        {
            if (HUD->Implements<URTSHUDInterface>())
            {
                return TScriptInterface<IRTSHUDInterface>(HUD);
            }
        }
    }
    return TScriptInterface<IRTSHUDInterface>();
}

TScriptInterface<IRTSPawnInterface> URTSInterfaceComponent::GetPawnInterface(AActor* TargetActor)
{
    if (TargetActor && TargetActor->Implements<URTSPawnInterface>())
    {
        return TScriptInterface<IRTSPawnInterface>(TargetActor);
    }
    return TScriptInterface<IRTSPawnInterface>();
}

TScriptInterface<IRTSUnitSelectionInterface> URTSInterfaceComponent::GetSelectionInterface()
{
    // RTSSelectionSystem removed - return empty interface
    return TScriptInterface<IRTSUnitSelectionInterface>();
}

bool URTSInterfaceComponent::DoesActorImplementPawnInterface(AActor* TargetActor)
{
    return TargetActor && TargetActor->Implements<URTSPawnInterface>();
}

bool URTSInterfaceComponent::DoesHUDImplementInterface(AHUD* TargetHUD)
{
    return TargetHUD && TargetHUD->Implements<URTSHUDInterface>();
}

// === Helper Functions ===

ARTSPlayerController* URTSInterfaceComponent::GetRTSPlayerController()
{
    if (UWorld* World = GetWorld())
    {
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            return Cast<ARTSPlayerController>(PC);
        }
    }
    return nullptr;
}

AHUD* URTSInterfaceComponent::GetHUD()
{
    if (ARTSPlayerController* PC = GetRTSPlayerController())
    {
        return PC->GetHUD();
    }
    return nullptr;
}
