#include "RTSSelectionSystem.h"
#include "RTSPlayerController.h"
#include "RTSUnit.h"
#include "RTSBuilding.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/PlayerController.h"

URTSSelectionSystem::URTSSelectionSystem()
{
    bHighlightSelectedUnits = true;
    bEnableDebugLogging = true;
}

void URTSSelectionSystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    SelectedUnits.Empty();
    SelectionGroups.Empty();
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSSelectionSystem: Initialized"));
    }
}

void URTSSelectionSystem::Deinitialize()
{
    ClearSelection();
    OwningPlayerController = nullptr;
    
    Super::Deinitialize();
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSSelectionSystem: Deinitialized"));
    }
}

void URTSSelectionSystem::InitializeSelectionSystem(ARTSPlayerController* PlayerController)
{
    SetPlayerController(PlayerController);
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSSelectionSystem: Initialized with PlayerController %s"), 
            PlayerController ? *PlayerController->GetName() : TEXT("None"));
    }
}

void URTSSelectionSystem::SetPlayerController(ARTSPlayerController* PlayerController)
{
    OwningPlayerController = PlayerController;
}

bool URTSSelectionSystem::SelectUnit(ARTSBaseActor* Unit, bool bAddToSelection)
{
    if (!Unit || !IsValid(Unit))
    {
        return false;
    }

    // Check if unit is alive
    if (!Unit->IsAlive())
    {
        return false;
    }

    // Store previous selection for event
    TArray<ARTSBaseActor*> PreviousSelection = SelectedUnits;

    // Clear selection if not adding
    if (!bAddToSelection)
    {
        for (ARTSBaseActor* SelectedUnit : SelectedUnits)
        {
            if (SelectedUnit && IsValid(SelectedUnit))
            {
                UpdateUnitSelectionState(SelectedUnit, false);
            }
        }
        SelectedUnits.Empty();
    }

    // Add unit if not already selected
    if (!SelectedUnits.Contains(Unit))
    {
        SelectedUnits.Add(Unit);
        UpdateUnitSelectionState(Unit, true);

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSSelectionSystem: Selected unit %s (Total: %d)"), 
                *Unit->GetName(), SelectedUnits.Num());
        }

        // Fire events
        OnSelectionChanged(SelectedUnits, PreviousSelection);
        OnSelectionChangedEvent.Broadcast(SelectedUnits, PreviousSelection);

        return true;
    }

    return false;
}

bool URTSSelectionSystem::DeselectUnit(ARTSBaseActor* Unit)
{
    if (!Unit || !IsValid(Unit))
    {
        return false;
    }

    if (SelectedUnits.Remove(Unit) > 0)
    {
        UpdateUnitSelectionState(Unit, false);

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSSelectionSystem: Deselected unit %s (Total: %d)"), 
                *Unit->GetName(), SelectedUnits.Num());
        }

        // Fire events
        TArray<ARTSBaseActor*> PreviousSelection = SelectedUnits;
        PreviousSelection.Add(Unit); // Add back for previous selection
        OnSelectionChanged(SelectedUnits, PreviousSelection);
        OnSelectionChangedEvent.Broadcast(SelectedUnits, PreviousSelection);

        return true;
    }

    return false;
}

int32 URTSSelectionSystem::SelectUnits(const TArray<ARTSBaseActor*>& Units, bool bAddToSelection)
{
    TArray<ARTSBaseActor*> PreviousSelection = SelectedUnits;
    int32 SelectedCount = 0;

    // Clear selection if not adding
    if (!bAddToSelection)
    {
        ClearSelection();
    }

    // Add each valid unit
    for (ARTSBaseActor* Unit : Units)
    {
        if (Unit && IsValid(Unit) && Unit->IsAlive() && !SelectedUnits.Contains(Unit))
        {
            SelectedUnits.Add(Unit);
            UpdateUnitSelectionState(Unit, true);
            SelectedCount++;
        }
    }

    if (SelectedCount > 0)
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSSelectionSystem: Selected %d units (Total: %d)"), 
                SelectedCount, SelectedUnits.Num());
        }

        // Fire events
        OnSelectionChanged(SelectedUnits, PreviousSelection);
        OnSelectionChangedEvent.Broadcast(SelectedUnits, PreviousSelection);
    }

    return SelectedCount;
}

void URTSSelectionSystem::ClearSelection()
{
    TArray<ARTSBaseActor*> PreviousSelection = SelectedUnits;

    // Clear selection state on all units
    for (ARTSBaseActor* Unit : SelectedUnits)
    {
        if (Unit && IsValid(Unit))
        {
            UpdateUnitSelectionState(Unit, false);
        }
    }

    SelectedUnits.Empty();

    if (bEnableDebugLogging && PreviousSelection.Num() > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSSelectionSystem: Cleared selection of %d units"), PreviousSelection.Num());
    }

    // Fire events
    OnSelectionChanged(SelectedUnits, PreviousSelection);
    OnSelectionChangedEvent.Broadcast(SelectedUnits, PreviousSelection);
}

TArray<ARTSBaseActor*> URTSSelectionSystem::GetSelectedUnits() const
{
    return SelectedUnits;
}

int32 URTSSelectionSystem::GetSelectedUnitCount() const
{
    return SelectedUnits.Num();
}

bool URTSSelectionSystem::IsUnitSelected(ARTSBaseActor* Unit) const
{
    return Unit && SelectedUnits.Contains(Unit);
}

void URTSSelectionSystem::UpdateUnitSelectionState(ARTSBaseActor* Unit, bool bSelected)
{
    if (Unit && IsValid(Unit))
    {
        Unit->SetSelected(bSelected);
    }
}

bool URTSSelectionSystem::PassesSelectionFilter(ARTSBaseActor* Unit, ERTSSelectionFilter Filter) const
{
    if (!Unit || !IsValid(Unit))
    {
        return false;
    }

    switch (Filter)
    {
        case ERTSSelectionFilter::None:
            return true;
        case ERTSSelectionFilter::Units:
            return Cast<ARTSUnit>(Unit) != nullptr;
        case ERTSSelectionFilter::Buildings:
            return Cast<ARTSBuilding>(Unit) != nullptr;
        case ERTSSelectionFilter::Military:
            return IsUnitMilitary(Unit);
        case ERTSSelectionFilter::Civilian:
            return !IsUnitMilitary(Unit);
        default:
            return true;
    }
}

TArray<ARTSBaseActor*> URTSSelectionSystem::GetAllSelectableUnits() const
{
    TArray<ARTSBaseActor*> SelectableUnits;
    TArray<AActor*> FoundActors;
    
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ARTSBaseActor::StaticClass(), FoundActors);
    
    for (AActor* Actor : FoundActors)
    {
        if (ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor))
        {
            if (RTSActor->IsAlive())
            {
                SelectableUnits.Add(RTSActor);
            }
        }
    }
    
    return SelectableUnits;
}

bool URTSSelectionSystem::IsUnitMilitary(ARTSBaseActor* Unit) const
{
    if (!Unit)
    {
        return false;
    }

    // Check if unit has weapons or is a military building
    if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
    {
        // Units with weapons are considered military
        return RTSUnit->HasWeapons();
    }
    else if (ARTSBuilding* Building = Cast<ARTSBuilding>(Unit))
    {
        // Buildings with weapons or specific types are military
        return Building->HasWeapons() || Building->GetBuildingType() == ERTSBuildingType::Defense;
    }

    return false;
}

int32 URTSSelectionSystem::SelectUnitsInBox(const FVector2D& StartPosition, const FVector2D& EndPosition, ERTSSelectionFilter Filter)
{
    TArray<ARTSBaseActor*> UnitsInBox = FindUnitsInSelectionBox(StartPosition, EndPosition, Filter);
    return SelectUnits(UnitsInBox, false);
}

int32 URTSSelectionSystem::SelectUnitsInRadius(const FVector& CenterLocation, float Radius, ERTSSelectionFilter Filter)
{
    TArray<ARTSBaseActor*> UnitsInRadius = FindUnitsInRadius(CenterLocation, Radius, Filter);
    return SelectUnits(UnitsInRadius, false);
}

TArray<ARTSBaseActor*> URTSSelectionSystem::FindUnitsInSelectionBox(const FVector2D& StartPos, const FVector2D& EndPos, ERTSSelectionFilter Filter)
{
    TArray<ARTSBaseActor*> UnitsInBox;

    if (!OwningPlayerController)
    {
        return UnitsInBox;
    }

    // Calculate box bounds
    FVector2D BoxMin = FVector2D(FMath::Min(StartPos.X, EndPos.X), FMath::Min(StartPos.Y, EndPos.Y));
    FVector2D BoxMax = FVector2D(FMath::Max(StartPos.X, EndPos.X), FMath::Max(StartPos.Y, EndPos.Y));

    // Get all selectable units
    TArray<ARTSBaseActor*> AllUnits = GetAllSelectableUnits();

    for (ARTSBaseActor* Unit : AllUnits)
    {
        if (!PassesSelectionFilter(Unit, Filter))
        {
            continue;
        }

        // Project unit location to screen space
        FVector2D ScreenPosition;
        if (OwningPlayerController->ProjectWorldLocationToScreen(Unit->GetActorLocation(), ScreenPosition))
        {
            // Check if unit is within selection box
            if (ScreenPosition.X >= BoxMin.X && ScreenPosition.X <= BoxMax.X &&
                ScreenPosition.Y >= BoxMin.Y && ScreenPosition.Y <= BoxMax.Y)
            {
                UnitsInBox.Add(Unit);
            }
        }
    }

    return UnitsInBox;
}

TArray<ARTSBaseActor*> URTSSelectionSystem::FindUnitsInRadius(const FVector& CenterLocation, float Radius, ERTSSelectionFilter Filter)
{
    TArray<ARTSBaseActor*> UnitsInRadius;
    TArray<ARTSBaseActor*> AllUnits = GetAllSelectableUnits();

    for (ARTSBaseActor* Unit : AllUnits)
    {
        if (!PassesSelectionFilter(Unit, Filter))
        {
            continue;
        }

        float Distance = FVector::Dist(Unit->GetActorLocation(), CenterLocation);
        if (Distance <= Radius)
        {
            UnitsInRadius.Add(Unit);
        }
    }

    return UnitsInRadius;
}

bool URTSSelectionSystem::SaveSelectionGroup(int32 GroupIndex)
{
    if (GroupIndex < 0 || GroupIndex > 9)
    {
        return false;
    }

    FRTSSelectionGroup& Group = SelectionGroups.FindOrAdd(GroupIndex);
    Group.GroupIndex = GroupIndex;
    Group.Units = SelectedUnits;
    Group.GroupName = FString::Printf(TEXT("Group %d"), GroupIndex);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSSelectionSystem: Saved selection group %d with %d units"),
            GroupIndex, SelectedUnits.Num());
    }

    return true;
}

bool URTSSelectionSystem::LoadSelectionGroup(int32 GroupIndex, bool bAddToSelection)
{
    if (GroupIndex < 0 || GroupIndex > 9)
    {
        return false;
    }

    const FRTSSelectionGroup* Group = SelectionGroups.Find(GroupIndex);
    if (!Group)
    {
        return false;
    }

    // Filter out invalid units
    TArray<ARTSBaseActor*> ValidUnits;
    for (ARTSBaseActor* Unit : Group->Units)
    {
        if (Unit && IsValid(Unit) && Unit->IsAlive())
        {
            ValidUnits.Add(Unit);
        }
    }

    if (ValidUnits.Num() > 0)
    {
        SelectUnits(ValidUnits, bAddToSelection);

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSSelectionSystem: Loaded selection group %d with %d units"),
                GroupIndex, ValidUnits.Num());
        }

        return true;
    }

    return false;
}

FRTSSelectionGroup URTSSelectionSystem::GetSelectionGroup(int32 GroupIndex) const
{
    if (const FRTSSelectionGroup* Group = SelectionGroups.Find(GroupIndex))
    {
        return *Group;
    }
    return FRTSSelectionGroup();
}

void URTSSelectionSystem::ClearSelectionGroup(int32 GroupIndex)
{
    if (SelectionGroups.Remove(GroupIndex) > 0)
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSSelectionSystem: Cleared selection group %d"), GroupIndex);
        }
    }
}

TArray<FRTSSelectionGroup> URTSSelectionSystem::GetAllSelectionGroups() const
{
    TArray<FRTSSelectionGroup> Groups;
    for (const auto& GroupPair : SelectionGroups)
    {
        Groups.Add(GroupPair.Value);
    }
    return Groups;
}

int32 URTSSelectionSystem::SelectAllUnitsOfType(ARTSBaseActor* ReferenceUnit, bool bOnScreen)
{
    if (!ReferenceUnit)
    {
        return 0;
    }

    TArray<ARTSBaseActor*> UnitsOfType;
    TArray<ARTSBaseActor*> AllUnits = GetAllSelectableUnits();

    for (ARTSBaseActor* Unit : AllUnits)
    {
        if (Unit->GetClass() == ReferenceUnit->GetClass())
        {
            if (!bOnScreen || IsUnitOnScreen(Unit))
            {
                UnitsOfType.Add(Unit);
            }
        }
    }

    return SelectUnits(UnitsOfType, false);
}

int32 URTSSelectionSystem::SelectAllIdleUnits()
{
    TArray<ARTSBaseActor*> IdleUnits;
    TArray<ARTSBaseActor*> AllUnits = GetAllSelectableUnits();

    for (ARTSBaseActor* Unit : AllUnits)
    {
        if (IsUnitIdle(Unit))
        {
            IdleUnits.Add(Unit);
        }
    }

    return SelectUnits(IdleUnits, false);
}

int32 URTSSelectionSystem::SelectAllMilitaryUnits()
{
    TArray<ARTSBaseActor*> MilitaryUnits;
    TArray<ARTSBaseActor*> AllUnits = GetAllSelectableUnits();

    for (ARTSBaseActor* Unit : AllUnits)
    {
        if (IsUnitMilitary(Unit))
        {
            MilitaryUnits.Add(Unit);
        }
    }

    return SelectUnits(MilitaryUnits, false);
}

TArray<ARTSBaseActor*> URTSSelectionSystem::FilterSelection(ERTSSelectionFilter Filter) const
{
    TArray<ARTSBaseActor*> FilteredUnits;

    for (ARTSBaseActor* Unit : SelectedUnits)
    {
        if (PassesSelectionFilter(Unit, Filter))
        {
            FilteredUnits.Add(Unit);
        }
    }

    return FilteredUnits;
}

void URTSSelectionSystem::SetSelectionHighlightVisibility(bool bVisible)
{
    bHighlightSelectedUnits = bVisible;
    UpdateSelectionHighlights();
}

void URTSSelectionSystem::UpdateSelectionHighlights()
{
    for (ARTSBaseActor* Unit : SelectedUnits)
    {
        if (Unit && IsValid(Unit))
        {
            UpdateUnitSelectionState(Unit, bHighlightSelectedUnits);
        }
    }
}

void URTSSelectionSystem::OnSelectionChanged(const TArray<ARTSBaseActor*>& NewSelection, const TArray<ARTSBaseActor*>& PreviousSelection)
{
    // This is a Blueprint implementable event - the implementation is in Blueprint
    // C++ classes can override this if needed
}

bool URTSSelectionSystem::IsUnitOnScreen(ARTSBaseActor* Unit) const
{
    if (!Unit || !OwningPlayerController)
    {
        return false;
    }

    FVector2D ScreenPosition;
    return OwningPlayerController->ProjectWorldLocationToScreen(Unit->GetActorLocation(), ScreenPosition);
}

bool URTSSelectionSystem::IsUnitIdle(ARTSBaseActor* Unit) const
{
    if (!Unit)
    {
        return false;
    }

    // Check if unit is moving or has orders
    if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
    {
        return !RTSUnit->IsMoving() && !RTSUnit->IsAttacking();
    }

    return true; // Buildings are considered "idle"
}
