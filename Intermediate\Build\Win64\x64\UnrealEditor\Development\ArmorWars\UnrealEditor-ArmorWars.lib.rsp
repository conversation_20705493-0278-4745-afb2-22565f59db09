/NOLOGO
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/DEF
/NAME:"UnrealEditor-ArmorWars.dll"
/IGNORE:4221
/NODEFAULTLIB
"F:/ArmorWars/Intermediate/Build/Win64/x64/ArmorWarsEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.1.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.2.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.3.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.4.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.5.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.6.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.7.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.8.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.9.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.10.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/ArmorWars.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAIController.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAIDebugHUD.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAircraftFlightComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAirMovementComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAirUnit.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSArmorComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSBaseActor.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSBehaviorNode.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSBehaviorNodes.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSBehaviorTreeComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSBuilding.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSEconomySubsystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSFactoryComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSFormationSystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSGameInstance.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSGameMode.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSGameState.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSGroupManager.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSHUD.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSLandMovementComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSLandUnit.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSPawn.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSPlayerController.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSPointDefenseComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSProjectile.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSSeaMovementComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSSeaUnit.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSSelectionSystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSTacticalAIComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSTeamManager.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSUnit.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSUnitAIComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSUnitDatabase.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSWeaponComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSWeaponController.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSInterfaceComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Default.rc2.res"
/OUT:"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/UnrealEditor-ArmorWars.lib"