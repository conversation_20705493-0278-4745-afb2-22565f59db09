// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSGameInstance.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSGameInstance() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSEconomySubsystem_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSGameInstance();
ARMORWARS_API UClass* Z_Construct_UClass_URTSGameInstance_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSTeamManager_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UGameInstance();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Class URTSGameInstance Function GetEconomySubsystem ****************************
struct Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics
{
	struct RTSGameInstance_eventGetEconomySubsystem_Parms
	{
		URTSEconomySubsystem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Game Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get the economy subsystem */" },
#endif
		{ "ModuleRelativePath", "Public/RTSGameInstance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get the economy subsystem" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSGameInstance_eventGetEconomySubsystem_Parms, ReturnValue), Z_Construct_UClass_URTSEconomySubsystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSGameInstance, nullptr, "GetEconomySubsystem", Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::RTSGameInstance_eventGetEconomySubsystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::RTSGameInstance_eventGetEconomySubsystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSGameInstance::execGetEconomySubsystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSEconomySubsystem**)Z_Param__Result=P_THIS->GetEconomySubsystem();
	P_NATIVE_END;
}
// ********** End Class URTSGameInstance Function GetEconomySubsystem ******************************

// ********** Begin Class URTSGameInstance Function GetTeamManager *********************************
struct Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics
{
	struct RTSGameInstance_eventGetTeamManager_Parms
	{
		URTSTeamManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Game Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get the team manager subsystem */" },
#endif
		{ "ModuleRelativePath", "Public/RTSGameInstance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get the team manager subsystem" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSGameInstance_eventGetTeamManager_Parms, ReturnValue), Z_Construct_UClass_URTSTeamManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSGameInstance, nullptr, "GetTeamManager", Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::RTSGameInstance_eventGetTeamManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::RTSGameInstance_eventGetTeamManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSGameInstance_GetTeamManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSGameInstance_GetTeamManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSGameInstance::execGetTeamManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSTeamManager**)Z_Param__Result=P_THIS->GetTeamManager();
	P_NATIVE_END;
}
// ********** End Class URTSGameInstance Function GetTeamManager ***********************************

// ********** Begin Class URTSGameInstance Function OnGameInstanceInitialized **********************
static FName NAME_URTSGameInstance_OnGameInstanceInitialized = FName(TEXT("OnGameInstanceInitialized"));
void URTSGameInstance::OnGameInstanceInitialized()
{
	UFunction* Func = FindFunctionChecked(NAME_URTSGameInstance_OnGameInstanceInitialized);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_URTSGameInstance_OnGameInstanceInitialized_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Game Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when the game instance is initialized */" },
#endif
		{ "ModuleRelativePath", "Public/RTSGameInstance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when the game instance is initialized" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSGameInstance_OnGameInstanceInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSGameInstance, nullptr, "OnGameInstanceInitialized", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSGameInstance_OnGameInstanceInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSGameInstance_OnGameInstanceInitialized_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSGameInstance_OnGameInstanceInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSGameInstance_OnGameInstanceInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class URTSGameInstance Function OnGameInstanceInitialized ************************

// ********** Begin Class URTSGameInstance Function OnGameInstanceShutdown *************************
static FName NAME_URTSGameInstance_OnGameInstanceShutdown = FName(TEXT("OnGameInstanceShutdown"));
void URTSGameInstance::OnGameInstanceShutdown()
{
	UFunction* Func = FindFunctionChecked(NAME_URTSGameInstance_OnGameInstanceShutdown);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_URTSGameInstance_OnGameInstanceShutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Game Instance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when the game instance is shutting down */" },
#endif
		{ "ModuleRelativePath", "Public/RTSGameInstance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when the game instance is shutting down" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSGameInstance_OnGameInstanceShutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSGameInstance, nullptr, "OnGameInstanceShutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSGameInstance_OnGameInstanceShutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSGameInstance_OnGameInstanceShutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSGameInstance_OnGameInstanceShutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSGameInstance_OnGameInstanceShutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class URTSGameInstance Function OnGameInstanceShutdown ***************************

// ********** Begin Class URTSGameInstance *********************************************************
void URTSGameInstance::StaticRegisterNativesURTSGameInstance()
{
	UClass* Class = URTSGameInstance::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetEconomySubsystem", &URTSGameInstance::execGetEconomySubsystem },
		{ "GetTeamManager", &URTSGameInstance::execGetTeamManager },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSGameInstance;
UClass* URTSGameInstance::GetPrivateStaticClass()
{
	using TClass = URTSGameInstance;
	if (!Z_Registration_Info_UClass_URTSGameInstance.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSGameInstance"),
			Z_Registration_Info_UClass_URTSGameInstance.InnerSingleton,
			StaticRegisterNativesURTSGameInstance,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSGameInstance.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSGameInstance_NoRegister()
{
	return URTSGameInstance::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSGameInstance_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Game Instance for ArmorWars RTS game\n * Manages game-wide systems and persistent data across levels\n */" },
#endif
		{ "IncludePath", "RTSGameInstance.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSGameInstance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Game Instance for ArmorWars RTS game\nManages game-wide systems and persistent data across levels" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether debug logging is enabled for the game instance */" },
#endif
		{ "ModuleRelativePath", "Public/RTSGameInstance.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether debug logging is enabled for the game instance" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSGameInstance_GetEconomySubsystem, "GetEconomySubsystem" }, // 1262682283
		{ &Z_Construct_UFunction_URTSGameInstance_GetTeamManager, "GetTeamManager" }, // 1377030266
		{ &Z_Construct_UFunction_URTSGameInstance_OnGameInstanceInitialized, "OnGameInstanceInitialized" }, // 3915869111
		{ &Z_Construct_UFunction_URTSGameInstance_OnGameInstanceShutdown, "OnGameInstanceShutdown" }, // 4238222405
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSGameInstance>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_URTSGameInstance_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((URTSGameInstance*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSGameInstance_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSGameInstance), &Z_Construct_UClass_URTSGameInstance_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSGameInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSGameInstance_Statics::NewProp_bEnableDebugLogging,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSGameInstance_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSGameInstance_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameInstance,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSGameInstance_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSGameInstance_Statics::ClassParams = {
	&URTSGameInstance::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSGameInstance_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSGameInstance_Statics::PropPointers),
	0,
	0x009000A8u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSGameInstance_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSGameInstance_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSGameInstance()
{
	if (!Z_Registration_Info_UClass_URTSGameInstance.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSGameInstance.OuterSingleton, Z_Construct_UClass_URTSGameInstance_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSGameInstance.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSGameInstance);
URTSGameInstance::~URTSGameInstance() {}
// ********** End Class URTSGameInstance ***********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h__Script_ArmorWars_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSGameInstance, URTSGameInstance::StaticClass, TEXT("URTSGameInstance"), &Z_Registration_Info_UClass_URTSGameInstance, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSGameInstance), 3169227162U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h__Script_ArmorWars_2534285736(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSGameInstance_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
