#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "Interfaces/RTSUnitSelectionInterface.h"
#include "RTSBaseActor.h"
#include "RTSSelectionSystem.generated.h"

class ARTSPlayerController;

// Delegate for selection changed events
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRTSSelectionChanged, const TArray<ARTSBaseActor*>&, NewSelection, const TArray<ARTSBaseActor*>&, PreviousSelection);

/**
 * RTS Selection System - Manages unit selection for RTS gameplay
 * Implements the IRTSUnitSelectionInterface for Blueprint compatibility
 */
UCLASS(BlueprintType)
class ARMORWARS_API URTSSelectionSystem : public UWorldSubsystem, public IRTSUnitSelectionInterface
{
    GENERATED_BODY()

public:
    URTSSelectionSystem();

    // UWorldSubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // IRTSUnitSelectionInterface implementation - these are BlueprintImplementableEvents
    // We implement them as regular C++ functions that can be called from Blueprint or C++
    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    void InitializeSelectionSystem(ARTSPlayerController* PlayerController);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    bool SelectUnit(ARTSBaseActor* Unit, bool bAddToSelection);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    bool DeselectUnit(ARTSBaseActor* Unit);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    int32 SelectUnits(const TArray<ARTSBaseActor*>& Units, bool bAddToSelection);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    int32 SelectUnitsInBox(const FVector2D& StartPosition, const FVector2D& EndPosition, ERTSSelectionFilter Filter);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    int32 SelectUnitsInRadius(const FVector& CenterLocation, float Radius, ERTSSelectionFilter Filter);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    void ClearSelection();

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    TArray<ARTSBaseActor*> GetSelectedUnits() const;

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    int32 GetSelectedUnitCount() const;

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    bool IsUnitSelected(ARTSBaseActor* Unit) const;

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    bool SaveSelectionGroup(int32 GroupIndex);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    bool LoadSelectionGroup(int32 GroupIndex, bool bAddToSelection);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    FRTSSelectionGroup GetSelectionGroup(int32 GroupIndex) const;

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    void ClearSelectionGroup(int32 GroupIndex);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    TArray<FRTSSelectionGroup> GetAllSelectionGroups() const;

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    int32 SelectAllUnitsOfType(ARTSBaseActor* ReferenceUnit, bool bOnScreen);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    int32 SelectAllIdleUnits();

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    int32 SelectAllMilitaryUnits();

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    TArray<ARTSBaseActor*> FilterSelection(ERTSSelectionFilter Filter) const;

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    void SetSelectionHighlightVisibility(bool bVisible);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    void UpdateSelectionHighlights();

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    void OnSelectionChanged(const TArray<ARTSBaseActor*>& NewSelection, const TArray<ARTSBaseActor*>& PreviousSelection);

    // Additional C++ methods for direct access
    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    void SetPlayerController(ARTSPlayerController* PlayerController);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    ARTSPlayerController* GetPlayerController() const { return OwningPlayerController; }

    // Selection box functionality
    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    TArray<ARTSBaseActor*> FindUnitsInSelectionBox(const FVector2D& StartPos, const FVector2D& EndPos, ERTSSelectionFilter Filter = ERTSSelectionFilter::None);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    TArray<ARTSBaseActor*> FindUnitsInRadius(const FVector& CenterLocation, float Radius, ERTSSelectionFilter Filter = ERTSSelectionFilter::None);

    // Selection events
    UPROPERTY(BlueprintAssignable, Category = "RTS Selection")
    FOnRTSSelectionChanged OnSelectionChangedEvent;

protected:
    // Currently selected units
    UPROPERTY(BlueprintReadOnly, Category = "Selection")
    TArray<ARTSBaseActor*> SelectedUnits;

    // Selection groups (hotkeys 1-9)
    UPROPERTY(BlueprintReadOnly, Category = "Selection")
    TMap<int32, FRTSSelectionGroup> SelectionGroups;

    // Owning player controller
    UPROPERTY()
    TObjectPtr<ARTSPlayerController> OwningPlayerController;

    // Selection settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection Settings")
    bool bHighlightSelectedUnits = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection Settings")
    bool bEnableDebugLogging = true;

    // Helper methods
    void UpdateUnitSelectionState(ARTSBaseActor* Unit, bool bSelected);
    bool PassesSelectionFilter(ARTSBaseActor* Unit, ERTSSelectionFilter Filter) const;
    TArray<ARTSBaseActor*> GetAllSelectableUnits() const;
    bool IsUnitOnScreen(ARTSBaseActor* Unit) const;
    bool IsUnitIdle(ARTSBaseActor* Unit) const;
    bool IsUnitMilitary(ARTSBaseActor* Unit) const;
};
